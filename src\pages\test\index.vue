<template>
  <view class="test-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="header-title">功能测试</text>
      <text class="header-desc">测试应用的各项功能</text>
    </view>
    
    <!-- 红包功能测试 -->
    <view class="test-section card">
      <view class="section-header">
        <text class="section-title">红包功能测试</text>
      </view>
      
      <view class="test-buttons">
        <button class="test-btn btn btn-danger" @click="testNewUserRedPacket">
          新用户红包
        </button>
        <button class="test-btn btn btn-primary" @click="testSignInRedPacket">
          签到红包
        </button>
        <button class="test-btn btn btn-success" @click="testTaskRedPacket">
          任务红包
        </button>
        <button class="test-btn btn btn-warning" @click="testSpecialRedPacket">
          特殊红包
        </button>
      </view>
    </view>
    
    <!-- 音效测试 -->
    <view class="test-section card">
      <view class="section-header">
        <text class="section-title">音效测试</text>
        <switch :checked="audioEnabled" @change="toggleAudio" />
      </view>
      
      <view class="test-buttons">
        <button class="test-btn btn btn-outline" @click="testSound('redpacket_open')">
          红包开启
        </button>
        <button class="test-btn btn btn-outline" @click="testSound('coin_drop')">
          金币掉落
        </button>
        <button class="test-btn btn btn-outline" @click="testSound('success')">
          成功提示
        </button>
        <button class="test-btn btn btn-outline" @click="testSound('click')">
          点击音效
        </button>
      </view>
    </view>
    
    <!-- 动画测试 -->
    <view class="test-section card">
      <view class="section-header">
        <text class="section-title">动画测试</text>
      </view>
      
      <view class="animation-demo">
        <view 
          class="demo-box" 
          :class="animationClass"
          @click="resetAnimation"
        >
          <text class="demo-text">点击重置</text>
        </view>
      </view>
      
      <view class="test-buttons">
        <button class="test-btn btn btn-outline" @click="testAnimation('bounce')">
          弹跳
        </button>
        <button class="test-btn btn btn-outline" @click="testAnimation('shake')">
          震动
        </button>
        <button class="test-btn btn btn-outline" @click="testAnimation('pulse')">
          脉冲
        </button>
        <button class="test-btn btn btn-outline" @click="testAnimation('rotate')">
          旋转
        </button>
      </view>
    </view>
    
    <!-- 存储测试 -->
    <view class="test-section card">
      <view class="section-header">
        <text class="section-title">存储测试</text>
      </view>
      
      <view class="storage-info">
        <view class="info-item">
          <text class="info-label">红包数据:</text>
          <text class="info-value">{{ redPacketCount }}个</text>
        </view>
        <view class="info-item">
          <text class="info-label">游戏统计:</text>
          <text class="info-value">{{ gameStats.totalGames }}局</text>
        </view>
        <view class="info-item">
          <text class="info-label">工具使用:</text>
          <text class="info-value">{{ workStats.totalUsage }}次</text>
        </view>
      </view>
      
      <view class="test-buttons">
        <button class="test-btn btn btn-outline" @click="clearAllData">
          清空所有数据
        </button>
        <button class="test-btn btn btn-outline" @click="exportData">
          导出数据
        </button>
      </view>
    </view>
    
    <!-- 性能测试 -->
    <view class="test-section card">
      <view class="section-header">
        <text class="section-title">性能测试</text>
      </view>
      
      <view class="performance-info">
        <view class="perf-item">
          <text class="perf-label">内存使用:</text>
          <text class="perf-value">{{ memoryUsage }}MB</text>
        </view>
        <view class="perf-item">
          <text class="perf-label">页面加载:</text>
          <text class="perf-value">{{ loadTime }}ms</text>
        </view>
        <view class="perf-item">
          <text class="perf-label">动画帧率:</text>
          <text class="perf-value">{{ fps }}fps</text>
        </view>
      </view>
      
      <view class="test-buttons">
        <button class="test-btn btn btn-outline" @click="runPerformanceTest">
          运行性能测试
        </button>
        <button class="test-btn btn btn-outline" @click="stressTest">
          压力测试
        </button>
      </view>
    </view>
    
    <!-- 测试日志 -->
    <view class="test-section card">
      <view class="section-header">
        <text class="section-title">测试日志</text>
        <button class="btn btn-small btn-outline" @click="clearLogs">清空</button>
      </view>
      
      <view class="log-container">
        <view 
          class="log-item" 
          v-for="(log, index) in logs" 
          :key="index"
          :class="log.type"
        >
          <text class="log-time">{{ formatTime(log.time) }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'
import { audioManager } from '@/utils/audio'

export default {
  data() {
    return {
      audioEnabled: true,
      animationClass: '',
      logs: [],
      
      // 性能数据
      memoryUsage: 0,
      loadTime: 0,
      fps: 60,
      
      // 统计数据
      redPacketCount: 0,
      gameStats: { totalGames: 0 },
      workStats: { totalUsage: 0 }
    }
  },
  
  computed: {
    redPacketStore() {
      return useRedPacketStore()
    }
  },
  
  onLoad() {
    this.initTest()
  },
  
  methods: {
    // 初始化测试
    initTest() {
      this.loadTime = Date.now()
      this.loadStats()
      this.addLog('info', '测试页面初始化完成')
      
      // 模拟加载时间
      setTimeout(() => {
        this.loadTime = Date.now() - this.loadTime
        this.addLog('success', `页面加载完成，耗时 ${this.loadTime}ms`)
      }, 100)
    },
    
    // 加载统计数据
    loadStats() {
      this.redPacketStore.initRedPacketData()
      this.redPacketCount = this.redPacketStore.totalCount
      this.gameStats = uni.getStorageSync('game_stats') || { totalGames: 0 }
      this.workStats = uni.getStorageSync('work_stats') || { totalUsage: 0 }
      
      // 模拟内存使用
      this.memoryUsage = Math.round(Math.random() * 50 + 20)
    },
    
    // 测试新用户红包
    testNewUserRedPacket() {
      this.addLog('info', '测试新用户红包')
      
      uni.$emit('show_redpacket', {
        type: 'newuser',
        amount: Math.floor(Math.random() * 50) + 10,
        title: '新用户红包',
        message: '欢迎使用多功能工具箱！',
        animation: 'bounce'
      })
      
      this.testSound('redpacket_open')
    },
    
    // 测试签到红包
    testSignInRedPacket() {
      this.addLog('info', '测试签到红包')
      
      uni.$emit('show_redpacket', {
        type: 'signin',
        amount: Math.floor(Math.random() * 10) + 1,
        title: '签到红包',
        message: '每日签到，好运连连！',
        animation: 'shake'
      })
      
      this.testSound('success')
    },
    
    // 测试任务红包
    testTaskRedPacket() {
      this.addLog('info', '测试任务红包')
      
      uni.$emit('show_redpacket', {
        type: 'task',
        amount: Math.floor(Math.random() * 8) + 2,
        title: '任务红包',
        message: '恭喜您完成任务！',
        animation: 'pulse'
      })
      
      this.testSound('coin_drop')
    },
    
    // 测试特殊红包
    testSpecialRedPacket() {
      this.addLog('info', '测试特殊红包')
      
      uni.$emit('show_redpacket', {
        type: 'special',
        amount: Math.floor(Math.random() * 100) + 15,
        title: '特殊红包',
        message: '恭喜您获得特殊奖励！',
        animation: 'rainbow'
      })
      
      this.testSound('redpacket_open')
    },
    
    // 测试音效
    testSound(soundName) {
      if (!this.audioEnabled) {
        this.addLog('warning', '音效已禁用')
        return
      }
      
      this.addLog('info', `播放音效: ${soundName}`)
      audioManager.play(soundName)
    },
    
    // 切换音效
    toggleAudio(e) {
      this.audioEnabled = e.detail.value
      audioManager.setEnabled(this.audioEnabled)
      this.addLog('info', `音效${this.audioEnabled ? '启用' : '禁用'}`)
    },
    
    // 测试动画
    testAnimation(type) {
      this.addLog('info', `测试动画: ${type}`)
      this.animationClass = `animate-${type}`
      
      setTimeout(() => {
        this.animationClass = ''
      }, 1000)
    },
    
    // 重置动画
    resetAnimation() {
      this.animationClass = ''
      this.addLog('info', '动画已重置')
    },
    
    // 清空所有数据
    clearAllData() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有应用数据吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            // 清空各种存储数据
            uni.clearStorageSync()
            
            // 重新初始化
            this.loadStats()
            this.addLog('warning', '所有数据已清空')
            
            uni.showToast({
              title: '数据已清空',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 导出数据
    exportData() {
      const data = {
        redPackets: this.redPacketStore.userRedPackets,
        gameStats: this.gameStats,
        workStats: this.workStats,
        exportTime: new Date().toISOString()
      }
      
      const dataStr = JSON.stringify(data, null, 2)
      
      // 在小程序中，可以通过剪贴板分享数据
      uni.setClipboardData({
        data: dataStr,
        success: () => {
          this.addLog('success', '数据已复制到剪贴板')
          uni.showToast({
            title: '数据已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 运行性能测试
    runPerformanceTest() {
      this.addLog('info', '开始性能测试')
      
      const startTime = Date.now()
      let frameCount = 0
      
      // 测试动画性能
      const testAnimation = () => {
        frameCount++
        if (frameCount < 60) {
          requestAnimationFrame(testAnimation)
        } else {
          const endTime = Date.now()
          const duration = endTime - startTime
          this.fps = Math.round(60000 / duration)
          
          this.addLog('success', `性能测试完成，FPS: ${this.fps}`)
        }
      }
      
      requestAnimationFrame(testAnimation)
    },
    
    // 压力测试
    stressTest() {
      this.addLog('info', '开始压力测试')
      
      // 连续播放多个音效
      for (let i = 0; i < 5; i++) {
        setTimeout(() => {
          this.testSound('click')
        }, i * 100)
      }
      
      // 连续触发多个动画
      const animations = ['bounce', 'shake', 'pulse', 'rotate']
      animations.forEach((anim, index) => {
        setTimeout(() => {
          this.testAnimation(anim)
        }, index * 200)
      })
      
      this.addLog('warning', '压力测试执行中...')
      
      setTimeout(() => {
        this.addLog('success', '压力测试完成')
      }, 2000)
    },
    
    // 添加日志
    addLog(type, message) {
      this.logs.unshift({
        type: type,
        message: message,
        time: Date.now()
      })
      
      // 只保留最近50条日志
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = []
      this.addLog('info', '日志已清空')
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.header-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 测试区域 */
.test-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

/* 测试按钮 */
.test-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.test-btn {
  padding: 24rpx 16rpx;
  font-size: 28rpx;
}

/* 动画演示 */
.animation-demo {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
}

.demo-box {
  width: 200rpx;
  height: 200rpx;
  background: var(--primary-color);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

/* 动画类 */
.animate-bounce {
  animation: testBounce 0.6s ease-in-out;
}

.animate-shake {
  animation: testShake 0.5s ease-in-out;
}

.animate-pulse {
  animation: testPulse 0.8s ease-in-out;
}

.animate-rotate {
  animation: testRotate 1s linear;
}

@keyframes testBounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -30rpx, 0); }
  70% { transform: translate3d(0, -15rpx, 0); }
  90% { transform: translate3d(0, -4rpx, 0); }
}

@keyframes testShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

@keyframes testPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes testRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 存储信息 */
.storage-info,
.performance-info {
  margin-bottom: 24rpx;
}

.info-item,
.perf-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child,
.perf-item:last-child {
  border-bottom: none;
}

.info-label,
.perf-label {
  font-size: 28rpx;
  color: var(--text-color);
}

.info-value,
.perf-value {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 日志容器 */
.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  background: #1a1a1a;
  border-radius: var(--radius);
  padding: 16rpx;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 8rpx 0;
  border-bottom: 1px solid #333;
  font-family: 'Courier New', monospace;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 24rpx;
  color: #888;
  min-width: 120rpx;
}

.log-message {
  font-size: 26rpx;
  flex: 1;
}

.log-item.info .log-message {
  color: #3498db;
}

.log-item.success .log-message {
  color: #2ecc71;
}

.log-item.warning .log-message {
  color: #f39c12;
}

.log-item.error .log-message {
  color: #e74c3c;
}

@media (max-width: 500rpx) {
  .test-buttons {
    grid-template-columns: 1fr;
  }
}
</style>

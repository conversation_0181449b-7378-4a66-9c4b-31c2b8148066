module.exports = {
  'resize-sensor': ['h5'],
  ad: ['mp-weixin'],
  audio: ['app-plus', 'mp-weixin', 'h5'],
  button: ['app-plus', 'mp-weixin', 'h5'],
  camera: ['mp-weixin'],
  canvas: ['app-plus', 'mp-weixin'],
  checkbox: ['app-plus', 'mp-weixin', 'h5'],
  'checkbox-group': ['app-plus', 'mp-weixin', 'h5'],
  'cover-image': ['app-plus', 'mp-weixin'],
  'cover-view': ['app-plus', 'mp-weixin'],
  editor: ['app-plus', 'mp-weixin', 'h5'],
  form: ['app-plus', 'mp-weixin', 'h5'],
  'functional-page-navigator': ['mp-weixin'],
  icon: ['app-plus', 'mp-weixin'],
  image: ['app-plus', 'mp-weixin', 'h5'],
  input: ['app-plus', 'mp-weixin', 'h5'],
  label: ['app-plus', 'mp-weixin', 'h5'],
  'live-player': ['mp-weixin'],
  'live-pusher': ['mp-weixin'],
  map: ['app-plus', 'mp-weixin', 'h5'],
  'movable-area': ['app-plus', 'mp-weixin'],
  'movable-view': ['app-plus', 'mp-weixin'],
  navigator: ['app-plus', 'mp-weixin', 'h5'],
  'official-account': ['mp-weixin'],
  'open-data': ['mp-weixin'],
  picker: ['app-plus', 'mp-weixin', 'h5'],
  'picker-view': ['app-plus', 'mp-weixin', 'h5'],
  'picker-view-column': ['app-plus', 'mp-weixin', 'h5'],
  progress: ['app-plus', 'mp-weixin', 'h5'],
  radio: ['app-plus', 'mp-weixin', 'h5'],
  'radio-group': ['app-plus', 'mp-weixin', 'h5'],
  'rich-text': ['app-plus', 'mp-weixin', 'h5'],
  'scroll-view': ['app-plus', 'mp-weixin', 'h5'],
  slider: ['app-plus', 'mp-weixin', 'h5'],
  swiper: ['app-plus', 'mp-weixin', 'h5'],
  'swiper-item': ['app-plus', 'mp-weixin', 'h5'],
  switch: ['app-plus', 'mp-weixin', 'h5'],
  text: ['app-plus', 'mp-weixin', 'h5'],
  textarea: ['app-plus', 'mp-weixin', 'h5'],
  video: ['app-plus', 'mp-weixin', 'h5'],
  view: ['app-plus', 'mp-weixin', 'h5'],
  'web-view': ['app-plus', 'mp-weixin']
}

!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).MakePlural=t()}}(function(){return function o(a,c,u){function s(n,t){if(!c[n]){if(!a[n]){var r="function"==typeof require&&require;if(!t&&r)return r(n,!0);if(l)return l(n,!0);var e=new Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e}var i=c[n]={exports:{}};a[n][0].call(i.exports,function(t){return s(a[n][1][t]||t)},i,i.exports,o,a,c,u)}return c[n].exports}for(var l="function"==typeof require&&require,t=0;t<u.length;t++)s(u[t]);return s}({1:[function(t,n,r){"use strict";function l(t){return function(t){if(Array.isArray(t)){for(var n=0,r=new Array(t.length);n<t.length;n++)r[n]=t[n];return r}}(t)||e(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function f(t){return function(t){if(Array.isArray(t))return t}(t)||e(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function e(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function i(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){for(var r=0;r<n.length;r++){var e=n[r];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}function a(t,n,r){return n&&o(t.prototype,n),r&&o(t,r),t}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var c=function(){function t(){i(this,t)}return a(t,[{key:"parse",value:function(t){var o=this;return"i = 0 or n = 1"===t?"n >= 0 && n <= 1":"i = 0,1"===t?"n >= 0 && n < 2":"i = 1 and v = 0"===t?(this.v0=1,"n == 1 && v0"):t.replace(/([tv]) (!?)= 0/g,function(t,n,r){var e=n+"0";return o[e]=1,r?"!"+e:e}).replace(/\b[fintv]\b/g,function(t){return o[t]=1,t}).replace(/([fin]) % (10+)/g,function(t,n,r){var e=n+r;return o[e]=1,e}).replace(/n10+ = 0/g,"t0 && $&").replace(/(\w+ (!?)= )([0-9.]+,[0-9.,]+)/g,function(t,n,r,e){return"n = 0,1"===t?"(n == 0 || n == 1)":r?n+e.split(",").join(" && "+n):"("+n+e.split(",").join(" || "+n)+")"}).replace(/(\w+) (!?)= ([0-9]+)\.\.([0-9]+)/g,function(t,n,r,e,i){return Number(e)+1===Number(i)?r?"".concat(n," != ").concat(e," && ").concat(n," != ").concat(i):"(".concat(n," == ").concat(e," || ").concat(n," == ").concat(i,")"):r?"(".concat(n," < ").concat(e," || ").concat(n," > ").concat(i,")"):"n"===n?(o.t0=1,"(t0 && n >= ".concat(e," && n <= ").concat(i,")")):"(".concat(n," >= ").concat(e," && ").concat(n," <= ").concat(i,")")}).replace(/ and /g," && ").replace(/ or /g," || ").replace(/ = /g," == ")}},{key:"vars",value:function(){var t=[];for(var n in this.i&&t.push("i = s[0]"),(this.f||this.v)&&t.push("f = s[1] || ''"),this.t&&t.push("t = (s[1] || '').replace(/0+$/, '')"),this.v&&t.push("v = f.length"),this.v0&&t.push("v0 = !s[1]"),(this.t0||this.n10||this.n100)&&t.push("t0 = Number(s[0]) == n"),this)if(/^.10+$/.test(n)){var r="n"===n[0]?"t0 && s[0]":n[0];t.push("".concat(n," = ").concat(r,".slice(-").concat(n.substr(2).length,")"))}return t.length?"var "+["s = String(n).split('.')"].concat(t).join(", "):""}}]),t}(),u=function(){function n(t){i(this,n),this.obj=t,this.ordinal={},this.cardinal={}}return a(n,[{key:"add",value:function(t,n,r){this[t][n]={src:r,values:null}}},{key:"testCond",value:function(t,n,r,e){try{var i=(e||this.obj.fn)(t,"ordinal"===n)}catch(t){i=t.toString()}if(i!==r)throw new Error("Locale "+JSON.stringify(this.obj.lc)+n+" rule self-test failed for v = "+JSON.stringify(t)+" (was "+JSON.stringify(i)+", expected "+JSON.stringify(r)+")");return!0}},{key:"testCat",value:function(n,r,e){var i=this,t=this[n][r];return t.values||(t.values=t.src.join(" ").replace(/^[ ,]+|[ ,…]+$/g,"").replace(/(0\.[0-9])~(1\.[1-9])/g,"$1 1.0 $2").split(/[ ,~…]+/)),t.values.forEach(function(t){i.testCond(t,n,r,e),/\.0+$/.test(t)||i.testCond(Number(t),n,r,e)}),!0}},{key:"testAll",value:function(){for(var t in this.cardinal)this.testCat("cardinal",t);for(var n in this.ordinal)this.testCat("ordinal",n);return!0}}]),n}(),s=function(){function s(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:s,r=n.cardinals,e=n.ordinals;if(i(this,s),!r&&!e)throw new Error("At least one type of plural is required");return this.lc=t,this.categories={cardinal:[],ordinal:[]},this.parser=new c,this.tests=new u(this),this.fn=this.buildFunction(r,e),(this.fn._obj=this).fn.categories=this.categories,this.fn.test=function(){return this.tests.testAll()&&this.fn}.bind(this),this.fn.toString=this.fnToString.bind(this),this.fn}return a(s,[{key:"compile",value:function(t,n){var r=[],e=s.getRules(t,this.lc);if(!e){if(n)throw new Error('Locale "'.concat(this.lc,'" ').concat(t," rules not found"));return this.categories[t]=["other"],"'other'"}for(var i in e){var o=f(e[i].trim().split(/\s*@\w*/)),a=o[0],c=o.slice(1),u=i.replace("pluralRule-count-","");a&&r.push([this.parser.parse(a),u]),this.tests.add(t,u,c)}return this.categories[t]=r.map(function(t){return t[1]}).concat("other"),1===r.length?"(".concat(r[0][0],") ? '").concat(r[0][1],"' : 'other'"):l(r.map(function(t){return"(".concat(t[0],") ? '").concat(t[1],"'")})).concat(["'other'"]).join("\n      : ")}},{key:"buildFunction",value:function(t,n){var r=this,e=function(t){return"  ".concat(t,";").replace(/(.{1,78})(,|$) ?/g,"$1$2\n      ")},i=function(t){return"  ".concat(t,";").replace(/(.{1,78}) (\|\| |$) ?/gm,"$1\n          $2")},o=[n&&["ordinal",!t],t&&["cardinal",!0]].map(function(t){return t?(t[1]?"return ":"if (ord) return ")+r.compile.apply(r,l(t)):""}).map(i),a=[e(this.parser.vars())].concat(l(o)).filter(function(t){return!/^[\s;]*$/.test(t)}).map(function(t){return t.replace(/\s+$/gm,"")}).join("\n");return new Function(n&&t?"n, ord":"n",a)}},{key:"fnToString",value:function(t){return Function.prototype.toString.call(this.fn).replace(/^function( \w+)?/,t?"function "+t:"function").replace(/\n\/\*(``)?\*\//,"")}}],[{key:"load",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach(function(t){var n=t&&t.supplemental||null;if(!n)throw new Error("Data does not appear to be CLDR data");s.rules={cardinal:n["plurals-type-cardinal"]||s.rules.cardinal,ordinal:n["plurals-type-ordinal"]||s.rules.ordinal}}),s}},{key:"getRules",value:function(t,n){if(n.length){var r=s.rules[t];if(n in r)return r[n];var e=n.toLowerCase();for(var i in r)if(i.toLowerCase()===e)return r[i]}return null}}]),s}();(r.default=s).cardinals=!0,s.ordinals=!1,s.rules={cardinal:{},ordinal:{}},n.exports=r.default},{}]},{},[1])(1)});
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "多功能工具箱",
        "navigationBarBackgroundColor": "#6366f1",
        "navigationBarTextStyle": "white",
        "backgroundColor": "#f8fafc"
      }
    },
    {
      "path": "pages/entertainment/index",
      "style": {
        "navigationBarTitleText": "娱乐游戏",
        "navigationBarBackgroundColor": "#ec4899",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/work/index",
      "style": {
        "navigationBarTitleText": "工作工具",
        "navigationBarBackgroundColor": "#10b981",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/life/index",
      "style": {
        "navigationBarTitleText": "生活助手",
        "navigationBarBackgroundColor": "#f59e0b",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/redpacket/index",
      "style": {
        "navigationBarTitleText": "我的红包",
        "navigationBarBackgroundColor": "#dc2626",
        "navigationBarTextStyle": "white"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/entertainment",
      "pages": [
        {
          "path": "chess/index",
          "style": {
            "navigationBarTitleText": "中国象棋"
          }
        },
        {
          "path": "gobang/index",
          "style": {
            "navigationBarTitleText": "五子棋"
          }
        },

      ]
    },
    {
      "root": "pages/work",
      "pages": [
        {
          "path": "idcard/index",
          "style": {
            "navigationBarTitleText": "证件号生成器"
          }
        },
        {
          "path": "watermark/image",
          "style": {
            "navigationBarTitleText": "图片去水印"
          }
        },
        {
          "path": "watermark/video",
          "style": {
            "navigationBarTitleText": "视频去水印"
          }
        }
      ]
    },
    {
      "root": "pages/life",
      "pages": [
        {
          "path": "calculator/index",
          "style": {
            "navigationBarTitleText": "计算器"
          }
        },
        {
          "path": "health/index",
          "style": {
            "navigationBarTitleText": "健康助手"
          }
        },
        {
          "path": "travel/index",
          "style": {
            "navigationBarTitleText": "出行助手"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#6366f1",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/entertainment/index",
        "iconPath": "static/tabbar/game.png",
        "selectedIconPath": "static/tabbar/game-active.png",
        "text": "娱乐"
      },
      {
        "pagePath": "pages/work/index",
        "iconPath": "static/tabbar/work.png",
        "selectedIconPath": "static/tabbar/work-active.png",
        "text": "工作"
      },
      {
        "pagePath": "pages/life/index",
        "iconPath": "static/tabbar/life.png",
        "selectedIconPath": "static/tabbar/life-active.png",
        "text": "生活"
      },
      {
        "pagePath": "pages/redpacket/index",
        "iconPath": "static/tabbar/redpacket.png",
        "selectedIconPath": "static/tabbar/redpacket-active.png",
        "text": "红包"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationBarTitleText": "多功能工具箱",
    "navigationBarBackgroundColor": "#6366f1",
    "backgroundColor": "#f8fafc",
    "app-plus": {
      "background": "#efeff4"
    }
  },
  "uniIdRouter": {}
}

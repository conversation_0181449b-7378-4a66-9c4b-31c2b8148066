{"name": "@dcloudio/vite-plugin-uni", "version": "3.0.0-3081220230817001", "description": "uni-app vite plugin", "bin": {"uni": "bin/uni.js"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["bin", "dist"], "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/vite-plugin-uni"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "dependencies": {"@babel/core": "^7.21.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-typescript": "^7.20.7", "@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/uni-shared": "3.0.0-3081220230817001", "@rollup/pluginutils": "^4.2.0", "@vitejs/plugin-legacy": "^4.0.3", "@vitejs/plugin-vue": "^4.2.1", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-core": "3.2.47", "@vue/compiler-dom": "3.2.47", "@vue/compiler-sfc": "3.2.47", "@vue/shared": "3.2.47", "cac": "6.7.9", "debug": "^4.3.3", "estree-walker": "^2.0.2", "express": "^4.17.1", "fast-glob": "^3.2.11", "fs-extra": "^10.0.0", "hash-sum": "^2.0.0", "jsonc-parser": "^3.0.0", "magic-string": "^0.30.0", "picocolors": "^1.0.0", "terser": "^5.4.0"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/estree": "^0.0.51", "@types/express": "^4.17.12", "@types/fs-extra": "^9.0.13", "@types/sass": "^1.16.0", "@vue/babel-plugin-jsx": "^1.1.1", "chokidar": "^3.5.3", "vite": "^4.0.0", "vue": "3.2.47"}, "peerDependencies": {"vite": "^4.0.0"}, "uni-app": {"compilerVersion": "3.8.12"}, "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da"}
const semver = require('semver')

let sassLoaderVersion
try {
  sassLoaderVersion = semver.major(require('sass-loader/package.json').version)
} catch (e) {}

const SCSS =
  `
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

$uni-text-color: #333;//基本色
$uni-text-color-inverse: #fff;//反色
$uni-text-color-grey: #999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1;//点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);//遮罩颜色

$uni-border-color: #c8c7cc;


$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title: 40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 36rpx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph: 30rpx;
`
const SASS =
  `
$uni-color-primary: #007aff
$uni-color-success: #4cd964
$uni-color-warning: #f0ad4e
$uni-color-error: #dd524d

$uni-text-color: #333//基本色
$uni-text-color-inverse: #fff//反色
$uni-text-color-grey: #999//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080
$uni-text-color-disable: #c0c0c0

$uni-bg-color: #ffffff
$uni-bg-color-grey: #f8f8f8
$uni-bg-color-hover: #f1f1f1//点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4)//遮罩颜色

$uni-border-color: #c8c7cc


$uni-font-size-sm: 24rpx
$uni-font-size-base: 28rpx
$uni-font-size-lg: 32rpx

$uni-img-size-sm: 40rpx
$uni-img-size-base: 52rpx
$uni-img-size-lg: 80rpx

$uni-border-radius-sm: 4rpx
$uni-border-radius-base: 6rpx
$uni-border-radius-lg: 12rpx
$uni-border-radius-circle: 50%

$uni-spacing-row-sm: 10px
$uni-spacing-row-base: 20rpx
$uni-spacing-row-lg: 30rpx

$uni-spacing-col-sm: 8rpx
$uni-spacing-col-base: 16rpx
$uni-spacing-col-lg: 24rpx

$uni-opacity-disabled: 0.3 // 组件禁用态的透明度

$uni-color-title: #2C405A // 文章标题颜色
$uni-font-size-title: 40rpx
$uni-color-subtitle: #555555 // 二级标题颜色
$uni-font-size-subtitle: 36rpx
$uni-color-paragraph: #3F536E // 文章段落颜色
$uni-font-size-paragraph: 30rpx
`

module.exports = {
  SCSS,
  SASS,
  sassLoaderVersion
}

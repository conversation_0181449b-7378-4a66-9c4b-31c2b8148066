var VueCompilerDOM=function(e){"use strict";function t(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const n=/;(?![^(]*\))/g,o=/:([^]+)/,r=/\/\*.*?\*\//gs;const s=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),i=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),c=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),l={},a=()=>{},p=()=>!1,u=/^on[^a-z]/,f=e=>u.test(e),d=Object.assign,h=Array.isArray,m=e=>"string"==typeof e,g=e=>"symbol"==typeof e,y=e=>null!==e&&"object"==typeof e,v=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),S=t("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),b=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,N=b((e=>e.replace(E,((e,t)=>t?t.toUpperCase():"")))),_=/\B([A-Z])/g,x=b((e=>e.replace(_,"-$1").toLowerCase())),T=b((e=>e.charAt(0).toUpperCase()+e.slice(1))),k=b((e=>e?`on${T(e)}`:""));function O(e){throw e}function C(e){}function I(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const M=Symbol(""),R=Symbol(""),P=Symbol(""),w=Symbol(""),$=Symbol(""),L=Symbol(""),V=Symbol(""),A=Symbol(""),D=Symbol(""),B=Symbol(""),F=Symbol(""),j=Symbol(""),H=Symbol(""),W=Symbol(""),K=Symbol(""),U=Symbol(""),J=Symbol(""),G=Symbol(""),z=Symbol(""),Y=Symbol(""),Z=Symbol(""),q=Symbol(""),X=Symbol(""),Q=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe=Symbol(""),re=Symbol(""),se=Symbol(""),ie=Symbol(""),ce=Symbol(""),le=Symbol(""),ae=Symbol(""),pe=Symbol(""),ue=Symbol(""),fe=Symbol(""),de=Symbol(""),he=Symbol(""),me={[M]:"Fragment",[R]:"Teleport",[P]:"Suspense",[w]:"KeepAlive",[$]:"BaseTransition",[L]:"openBlock",[V]:"createBlock",[A]:"createElementBlock",[D]:"createVNode",[B]:"createElementVNode",[F]:"createCommentVNode",[j]:"createTextVNode",[H]:"createStaticVNode",[W]:"resolveComponent",[K]:"resolveDynamicComponent",[U]:"resolveDirective",[J]:"resolveFilter",[G]:"withDirectives",[z]:"renderList",[Y]:"renderSlot",[Z]:"createSlots",[q]:"toDisplayString",[X]:"mergeProps",[Q]:"normalizeClass",[ee]:"normalizeStyle",[te]:"normalizeProps",[ne]:"guardReactiveProps",[oe]:"toHandlers",[re]:"camelize",[se]:"capitalize",[ie]:"toHandlerKey",[ce]:"setBlockTracking",[le]:"pushScopeId",[ae]:"popScopeId",[pe]:"withCtx",[ue]:"unref",[fe]:"isRef",[de]:"withMemo",[he]:"isMemoSame"};function ge(e){Object.getOwnPropertySymbols(e).forEach((t=>{me[t]=e[t]}))}const ye={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function ve(e,t=ye){return{type:0,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function Se(e,t,n,o,r,s,i,c=!1,l=!1,a=!1,p=ye){return e&&(c?(e.helper(L),e.helper(Qe(e.inSSR,a))):e.helper(Xe(e.inSSR,a)),i&&e.helper(G)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:i,isBlock:c,disableTracking:l,isComponent:a,loc:p}}function be(e,t=ye){return{type:17,loc:t,elements:e}}function Ee(e,t=ye){return{type:15,loc:t,properties:e}}function Ne(e,t){return{type:16,loc:ye,key:m(e)?_e(e,!0):e,value:t}}function _e(e,t=!1,n=ye,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function xe(e,t=ye){return{type:8,loc:t,children:e}}function Te(e,t=[],n=ye){return{type:14,loc:n,callee:e,arguments:t}}function ke(e,t,n=!1,o=!1,r=ye){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function Oe(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:ye}}function Ce(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:ye}}function Ie(e){return{type:21,body:e,loc:ye}}const Me=e=>4===e.type&&e.isStatic,Re=(e,t)=>e===t||e===x(t);function Pe(e){return Re(e,"Teleport")?R:Re(e,"Suspense")?P:Re(e,"KeepAlive")?w:Re(e,"BaseTransition")?$:void 0}const we=/^\d|[^\$\w]/,$e=e=>!we.test(e),Le=/[A-Za-z_$\xA0-\uFFFF]/,Ve=/[\.\?\w$\xA0-\uFFFF]/,Ae=/\s+[.[]\s*|\s*[.[]\s+/g,De=e=>{e=e.trim().replace(Ae,(e=>e.trim()));let t=0,n=[],o=0,r=0,s=null;for(let i=0;i<e.length;i++){const c=e.charAt(i);switch(t){case 0:if("["===c)n.push(t),t=1,o++;else if("("===c)n.push(t),t=2,r++;else if(!(0===i?Le:Ve).test(c))return!1;break;case 1:"'"===c||'"'===c||"`"===c?(n.push(t),t=3,s=c):"["===c?o++:"]"===c&&(--o||(t=n.pop()));break;case 2:if("'"===c||'"'===c||"`"===c)n.push(t),t=3,s=c;else if("("===c)r++;else if(")"===c){if(i===e.length-1)return!1;--r||(t=n.pop())}break;case 3:c===s&&(t=n.pop(),s=null)}}return!o&&!r},Be=a,Fe=De;function je(e,t,n){const o={source:e.source.slice(t,t+n),start:He(e.start,e.source,t),end:e.end};return null!=n&&(o.end=He(e.start,e.source,t+n)),o}function He(e,t,n=t.length){return We(d({},e),t,n)}function We(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function Ke(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(m(t)?r.name===t:t.test(r.name)))return r}}function Ue(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Je(s.arg,t))return s}}function Je(e,t){return!(!e||!Me(e)||e.content!==t)}function Ge(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function ze(e){return 5===e.type||2===e.type}function Ye(e){return 7===e.type&&"slot"===e.name}function Ze(e){return 1===e.type&&3===e.tagType}function qe(e){return 1===e.type&&2===e.tagType}function Xe(e,t){return e||t?D:B}function Qe(e,t){return e||t?V:A}const et=new Set([te,ne]);function tt(e,t=[]){if(e&&!m(e)&&14===e.type){const n=e.callee;if(!m(n)&&et.has(n))return tt(e.arguments[0],t.concat(e))}return[e,t]}function nt(e,t,n){let o,r,s=13===e.type?e.props:e.arguments[2],i=[];if(s&&!m(s)&&14===s.type){const e=tt(s);s=e[0],i=e[1],r=i[i.length-1]}if(null==s||m(s))o=Ee([t]);else if(14===s.type){const e=s.arguments[0];m(e)||15!==e.type?s.callee===oe?o=Te(n.helper(X),[Ee([t]),s]):s.arguments.unshift(Ee([t])):ot(t,e)||e.properties.unshift(t),!o&&(o=s)}else 15===s.type?(ot(t,s)||s.properties.unshift(t),o=s):(o=Te(n.helper(X),[Ee([t]),s]),r&&r.callee===ne&&(r=i[i.length-2]));13===e.type?r?r.arguments[0]=o:e.props=o:r?r.arguments[0]=o:e.arguments[2]=o}function ot(e,t){let n=!1;if(4===e.key.type){const o=e.key.content;n=t.properties.some((e=>4===e.key.type&&e.key.content===o))}return n}function rt(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}function st(e){return 14===e.type&&e.callee===de?e.arguments[1].returns:e}function it(e,{helper:t,removeHelper:n,inSSR:o}){e.isBlock||(e.isBlock=!0,n(Xe(o,e.isComponent)),t(L),t(Qe(o,e.isComponent)))}const ct={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_PROP:{message:".prop modifier for v-bind has been removed and no longer necessary. Vue 3 will automatically set a binding as DOM property when appropriate."},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTER:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function lt(e,t){const n=t.options?t.options.compatConfig:t.compatConfig,o=n&&n[e];return"MODE"===e?o||3:o}function at(e,t){const n=lt("MODE",t),o=lt(e,t);return 3===n?!0===o:!1!==o}function pt(e,t,n,...o){return at(e,t)}const ut=/&(gt|lt|amp|apos|quot);/g,ft={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},dt={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:p,isPreTag:p,isCustomElement:p,decodeEntities:e=>e.replace(ut,((e,t)=>ft[t])),onError:O,onWarn:C,comments:!1};function ht(e,t={}){const n=function(e,t){const n=d({},dt);let o;for(o in t)n[o]=void 0===t[o]?dt[o]:t[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1,onWarn:n.onWarn}}(e,t),o=Ct(n);return ve(mt(n,0,[]),It(n,o))}function mt(e,t,n){const o=Mt(n),r=o?o.ns:0,s=[];for(;!Lt(e,t,n);){const i=e.source;let c;if(0===t||1===t)if(!e.inVPre&&Rt(i,e.options.delimiters[0]))c=Tt(e,t);else if(0===t&&"<"===i[0])if(1===i.length);else if("!"===i[1])c=Rt(i,"\x3c!--")?vt(e):Rt(i,"<!DOCTYPE")?St(e):Rt(i,"<![CDATA[")&&0!==r?yt(e,n):St(e);else if("/"===i[1])if(2===i.length);else{if(">"===i[2]){Pt(e,3);continue}if(/[a-z]/i.test(i[2])){Nt(e,1,o);continue}c=St(e)}else/[a-z]/i.test(i[1])?(c=bt(e,n),at("COMPILER_NATIVE_TEMPLATE",e)&&c&&"template"===c.tag&&!c.props.some((e=>7===e.type&&Et(e.name)))&&(c=c.children)):"?"===i[1]&&(c=St(e));if(c||(c=kt(e,t)),h(c))for(let e=0;e<c.length;e++)gt(s,c[e]);else gt(s,c)}let i=!1;if(2!==t&&1!==t){const t="preserve"!==e.options.whitespace;for(let n=0;n<s.length;n++){const o=s[n];if(2===o.type)if(e.inPre)o.content=o.content.replace(/\r\n/g,"\n");else if(/[^\t\r\n\f ]/.test(o.content))t&&(o.content=o.content.replace(/[\t\r\n\f ]+/g," "));else{const e=s[n-1],r=s[n+1];!e||!r||t&&(3===e.type&&3===r.type||3===e.type&&1===r.type||1===e.type&&3===r.type||1===e.type&&1===r.type&&/[\r\n]/.test(o.content))?(i=!0,s[n]=null):o.content=" "}else 3!==o.type||e.options.comments||(i=!0,s[n]=null)}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return i?s.filter(Boolean):s}function gt(e,t){if(2===t.type){const n=Mt(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function yt(e,t){Pt(e,9);const n=mt(e,3,t);return 0===e.source.length||Pt(e,3),n}function vt(e){const t=Ct(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)Pt(e,s-r+1),r=s+1;Pt(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),Pt(e,e.source.length);return{type:3,content:n,loc:It(e,t)}}function St(e){const t=Ct(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),Pt(e,e.source.length)):(o=e.source.slice(n,r),Pt(e,r+1)),{type:3,content:o,loc:It(e,t)}}function bt(e,t){const n=e.inPre,o=e.inVPre,r=Mt(t),s=Nt(e,0,r),i=e.inPre&&!n,c=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return i&&(e.inPre=!1),c&&(e.inVPre=!1),s;t.push(s);const l=e.options.getTextMode(s,r),a=mt(e,l,t);t.pop();{const t=s.props.find((e=>6===e.type&&"inline-template"===e.name));if(t&&pt("COMPILER_INLINE_TEMPLATE",e)){const n=It(e,s.loc.end);t.value={type:2,content:n.source,loc:n}}}if(s.children=a,Vt(e.source,s.tag))Nt(e,1,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=a[0];e&&Rt(e.loc.source,"\x3c!--")}return s.loc=It(e,s.loc.start),i&&(e.inPre=!1),c&&(e.inVPre=!1),s}const Et=t("if,else,else-if,for,slot");function Nt(e,t,n){const o=Ct(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],i=e.options.getNamespace(s,n);Pt(e,r[0].length),wt(e);const c=Ct(e),l=e.source;e.options.isPreTag(s)&&(e.inPre=!0);let a=_t(e,t);0===t&&!e.inVPre&&a.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,d(e,c),e.source=l,a=_t(e,t).filter((e=>"v-pre"!==e.name)));let p=!1;if(0===e.source.length||(p=Rt(e.source,"/>"),Pt(e,p?2:1)),1===t)return;let u=0;return e.inVPre||("slot"===s?u=2:"template"===s?a.some((e=>7===e.type&&Et(e.name)))&&(u=3):function(e,t,n){const o=n.options;if(o.isCustomElement(e))return!1;if("component"===e||/^[A-Z]/.test(e)||Pe(e)||o.isBuiltInComponent&&o.isBuiltInComponent(e)||o.isNativeTag&&!o.isNativeTag(e))return!0;for(let r=0;r<t.length;r++){const e=t[r];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(pt("COMPILER_IS_ON_ELEMENT",n))return!0}}else{if("is"===e.name)return!0;if("bind"===e.name&&Je(e.arg,"is")&&pt("COMPILER_IS_ON_ELEMENT",n))return!0}}}(s,a,e)&&(u=1)),{type:1,ns:i,tag:s,tagType:u,props:a,isSelfClosing:p,children:[],loc:It(e,o),codegenNode:void 0}}function _t(e,t){const n=[],o=new Set;for(;e.source.length>0&&!Rt(e.source,">")&&!Rt(e.source,"/>");){if(Rt(e.source,"/")){Pt(e,1),wt(e);continue}const r=xt(e,o);6===r.type&&r.value&&"class"===r.name&&(r.value.content=r.value.content.replace(/\s+/g," ").trim()),0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),wt(e)}return n}function xt(e,t){const n=Ct(e),o=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(o),t.add(o);{const e=/["'<]/g;let t;for(;t=e.exec(o););}let r;Pt(e,o.length),/^[\t\r\n\f ]*=/.test(e.source)&&(wt(e),Pt(e,1),wt(e),r=function(e){const t=Ct(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){Pt(e,1);const t=e.source.indexOf(o);-1===t?n=Ot(e,e.source.length,4):(n=Ot(e,t,4),Pt(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]););n=Ot(e,t[0].length,4)}return{content:n,isQuoted:r,loc:It(e,t)}}(e));const s=It(e,n);if(!e.inVPre&&/^(v-[A-Za-z0-9-]|:|\.|@|#)/.test(o)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^\.|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(o);let i,c=Rt(o,"."),l=t[1]||(c||Rt(o,":")?"bind":Rt(o,"@")?"on":"slot");if(t[2]){const r="slot"===l,s=o.lastIndexOf(t[2]),c=It(e,$t(e,n,s),$t(e,n,s+t[2].length+(r&&t[3]||"").length));let a=t[2],p=!0;a.startsWith("[")?(p=!1,a=a.endsWith("]")?a.slice(1,a.length-1):a.slice(1)):r&&(a+=t[3]||""),i={type:4,content:a,isStatic:p,constType:p?3:0,loc:c}}if(r&&r.isQuoted){const e=r.loc;e.start.offset++,e.start.column++,e.end=He(e.start,r.content),e.source=e.source.slice(1,-1)}const a=t[3]?t[3].slice(1).split("."):[];return c&&a.push("prop"),"bind"===l&&i&&a.includes("sync")&&pt("COMPILER_V_BIND_SYNC",e,0)&&(l="model",a.splice(a.indexOf("sync"),1)),{type:7,name:l,exp:r&&{type:4,content:r.content,isStatic:!1,constType:0,loc:r.loc},arg:i,modifiers:a,loc:s}}return!e.inVPre&&Rt(o,"v-"),{type:6,name:o,value:r&&{type:2,content:r.content,loc:r.loc},loc:s}}function Tt(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=Ct(e);Pt(e,n.length);const i=Ct(e),c=Ct(e),l=r-n.length,a=e.source.slice(0,l),p=Ot(e,l,t),u=p.trim(),f=p.indexOf(u);f>0&&We(i,a,f);return We(c,a,l-(p.length-u.length-f)),Pt(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:u,loc:It(e,i,c)},loc:It(e,s)}}function kt(e,t){const n=3===t?["]]>"]:["<",e.options.delimiters[0]];let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=Ct(e);return{type:2,content:Ot(e,o,t),loc:It(e,r)}}function Ot(e,t,n){const o=e.source.slice(0,t);return Pt(e,t),2!==n&&3!==n&&o.includes("&")?e.options.decodeEntities(o,4===n):o}function Ct(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function It(e,t,n){return{start:t,end:n=n||Ct(e),source:e.originalSource.slice(t.offset,n.offset)}}function Mt(e){return e[e.length-1]}function Rt(e,t){return e.startsWith(t)}function Pt(e,t){const{source:n}=e;We(e,n,t),e.source=n.slice(t)}function wt(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&Pt(e,t[0].length)}function $t(e,t,n){return He(t,e.originalSource.slice(t.offset,n),n)}function Lt(e,t,n){const o=e.source;switch(t){case 0:if(Rt(o,"</"))for(let e=n.length-1;e>=0;--e)if(Vt(o,n[e].tag))return!0;break;case 1:case 2:{const e=Mt(n);if(e&&Vt(o,e.tag))return!0;break}case 3:if(Rt(o,"]]>"))return!0}return!o}function Vt(e,t){return Rt(e,"</")&&e.slice(2,2+t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function At(e,t){Bt(e,t,Dt(e,e.children[0]))}function Dt(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!qe(t)}function Bt(e,t,n=!1){const{children:o}=e,r=o.length;let s=0;for(let i=0;i<o.length;i++){const e=o[i];if(1===e.type&&0===e.tagType){const o=n?0:Ft(e,t);if(o>0){if(o>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),s++;continue}}else{const n=e.codegenNode;if(13===n.type){const o=Ut(n);if((!o||512===o||1===o)&&Wt(e,t)>=2){const o=Kt(e);o&&(n.props=t.hoist(o))}n.dynamicProps&&(n.dynamicProps=t.hoist(n.dynamicProps))}}}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,Bt(e,t),n&&t.scopes.vSlot--}else if(11===e.type)Bt(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)Bt(e.branches[n],t,1===e.branches[n].children.length)}s&&t.transformHoist&&t.transformHoist(o,t,e),s&&s===r&&1===e.type&&0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&h(e.codegenNode.children)&&(e.codegenNode.children=t.hoist(be(e.codegenNode.children)))}function Ft(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(r.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag)return 0;if(Ut(r))return n.set(e,0),0;{let o=3;const s=Wt(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=Ft(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=Ft(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}if(r.isBlock){for(let t=0;t<e.props.length;t++){if(7===e.props[t].type)return n.set(e,0),0}t.removeHelper(L),t.removeHelper(Qe(t.inSSR,r.isComponent)),r.isBlock=!1,t.helper(Xe(t.inSSR,r.isComponent))}return n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return Ft(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(m(o)||g(o))continue;const r=Ft(o,t);if(0===r)return 0;r<s&&(s=r)}return s}}const jt=new Set([Q,ee,te,ne]);function Ht(e,t){if(14===e.type&&!m(e.callee)&&jt.has(e.callee)){const n=e.arguments[0];if(4===n.type)return Ft(n,t);if(14===n.type)return Ht(n,t)}return 0}function Wt(e,t){let n=3;const o=Kt(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],i=Ft(r,t);if(0===i)return i;let c;if(i<n&&(n=i),c=4===s.type?Ft(s,t):14===s.type?Ht(s,t):0,0===c)return c;c<n&&(n=c)}}return n}function Kt(e){const t=e.codegenNode;if(13===t.type)return t.props}function Ut(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function Jt(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:o=!1,cacheHandlers:r=!1,nodeTransforms:s=[],directiveTransforms:i={},transformHoist:c=null,isBuiltInComponent:p=a,isCustomElement:u=a,expressionPlugins:f=[],scopeId:d=null,slotted:h=!0,ssr:g=!1,inSSR:y=!1,ssrCssVars:v="",bindingMetadata:S=l,inline:b=!1,isTS:E=!1,onError:_=O,onWarn:x=C,compatConfig:k}){const I=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),M={selfName:I&&T(N(I[1])),prefixIdentifiers:n,hoistStatic:o,cacheHandlers:r,nodeTransforms:s,directiveTransforms:i,transformHoist:c,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:f,scopeId:d,slotted:h,ssr:g,inSSR:y,ssrCssVars:v,bindingMetadata:S,inline:b,isTS:E,onError:_,onWarn:x,compatConfig:k,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=M.helpers.get(e)||0;return M.helpers.set(e,t+1),e},removeHelper(e){const t=M.helpers.get(e);if(t){const n=t-1;n?M.helpers.set(e,n):M.helpers.delete(e)}},helperString:e=>`_${me[M.helper(e)]}`,replaceNode(e){M.parent.children[M.childIndex]=M.currentNode=e},removeNode(e){const t=e?M.parent.children.indexOf(e):M.currentNode?M.childIndex:-1;e&&e!==M.currentNode?M.childIndex>t&&(M.childIndex--,M.onNodeRemoved()):(M.currentNode=null,M.onNodeRemoved()),M.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){m(e)&&(e=_e(e)),M.hoists.push(e);const t=_e(`_hoisted_${M.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>Ce(M.cached++,e,t)};return M.filters=new Set,M}function Gt(e,t){const n=Jt(e,t);zt(e,n),t.hoistStatic&&At(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:o}=e;if(1===o.length){const n=o[0];if(Dt(e,n)&&n.codegenNode){const o=n.codegenNode;13===o.type&&it(o,t),e.codegenNode=o}else e.codegenNode=n}else if(o.length>1){let o=64;e.codegenNode=Se(t,n(M),void 0,e.children,o+"",void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.filters=[...n.filters]}function zt(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(h(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(F);break;case 5:t.ssr||t.helper(q);break;case 9:for(let n=0;n<e.branches.length;n++)zt(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];m(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,zt(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function Yt(e,t){const n=m(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(Ye))return;const s=[];for(let i=0;i<r.length;i++){const c=r[i];if(7===c.type&&n(c.name)){r.splice(i,1),i--;const n=t(e,c,o);n&&s.push(n)}}return s}}}const Zt="/*#__PURE__*/",qt=e=>`${me[e]}: _${me[e]}`;function Xt(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:i=!1,runtimeGlobalName:c="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:a="vue/server-renderer",ssr:p=!1,isTS:u=!1,inSSR:f=!1}){const d={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:i,runtimeGlobalName:c,runtimeModuleName:l,ssrRuntimeModuleName:a,ssr:p,isTS:u,inSSR:f,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${me[e]}`,push(e,t){d.code+=e},indent(){h(++d.indentLevel)},deindent(e=!1){e?--d.indentLevel:h(--d.indentLevel)},newline(){h(d.indentLevel)}};function h(e){d.push("\n"+"  ".repeat(e))}return d}function Qt(e,t={}){const n=Xt(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:i,deindent:c,newline:l,ssr:a}=n,p=Array.from(e.helpers),u=p.length>0,f=!s&&"module"!==o,d=n;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,i=Array.from(e.helpers);if(i.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[D,B,F,j,H].filter((e=>i.includes(e))).map(qt).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o();for(let r=0;r<e.length;r++){const s=e[r];s&&(n(`const _hoisted_${r+1} = `),on(s,t),o())}t.pure=!1})(e.hoists,t),o(),n("return ")}(e,d);if(r(`function ${a?"ssrRender":"render"}(${(a?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),f&&(r("with (_ctx) {"),i(),u&&(r(`const { ${p.map(qt).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(en(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(en(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),en(e.filters,"filter",n),l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),a||r("return "),e.codegenNode?on(e.codegenNode,n):r("null"),f&&(c(),r("}")),c(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function en(e,t,{helper:n,push:o,newline:r,isTS:s}){const i=n("filter"===t?J:"component"===t?W:U);for(let c=0;c<e.length;c++){let n=e[c];const l=n.endsWith("__self");l&&(n=n.slice(0,-6)),o(`const ${rt(n,t)} = ${i}(${JSON.stringify(n)}${l?", true":""})${s?"!":""}`),c<e.length-1&&r()}}function tn(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),nn(e,t,n),n&&t.deindent(),t.push("]")}function nn(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let i=0;i<e.length;i++){const c=e[i];m(c)?r(c):h(c)?tn(c,t):on(c,t),i<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function on(e,t){if(m(e))t.push(e);else if(g(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:on(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:rn(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(Zt);n(`${o(q)}(`),on(e.content,t),n(")")}(e,t);break;case 8:sn(e,t);break;case 3:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(Zt);n(`${o(F)}(${JSON.stringify(e.content)})`,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:i,children:c,patchFlag:l,dynamicProps:a,directives:p,isBlock:u,disableTracking:f,isComponent:d}=e;p&&n(o(G)+"(");u&&n(`(${o(L)}(${f?"true":""}), `);r&&n(Zt);const h=u?Qe(t.inSSR,d):Xe(t.inSSR,d);n(o(h)+"(",e),nn(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,i,c,l,a]),t),n(")"),u&&n(")");p&&(n(", "),on(p,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=m(e.callee)?e.callee:o(e.callee);r&&n(Zt);n(s+"(",e),nn(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:i}=e;if(!i.length)return void n("{}",e);const c=i.length>1||!1;n(c?"{":"{ "),c&&o();for(let l=0;l<i.length;l++){const{key:e,value:o}=i[l];cn(e,t),n(": "),on(o,t),l<i.length-1&&(n(","),s())}c&&r(),n(c?"}":" }")}(e,t);break;case 17:!function(e,t){tn(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:i,body:c,newline:l,isSlot:a}=e;a&&n(`_${me[pe]}(`);n("(",e),h(s)?nn(s,t):s&&on(s,t);n(") => "),(l||c)&&(n("{"),o());i?(l&&n("return "),h(i)?tn(i,t):on(i,t)):c&&on(c,t);(l||c)&&(r(),n("}"));a&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:i,indent:c,deindent:l,newline:a}=t;if(4===n.type){const e=!$e(n.content);e&&i("("),rn(n,t),e&&i(")")}else i("("),on(n,t),i(")");s&&c(),t.indentLevel++,s||i(" "),i("? "),on(o,t),t.indentLevel--,s&&a(),s||i(" "),i(": ");const p=19===r.type;p||t.indentLevel++;on(r,t),p||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:i}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(ce)}(-1),`),i());n(`_cache[${e.index}] = `),on(e.value,t),e.isVNode&&(n(","),i(),n(`${o(ce)}(1),`),i(),n(`_cache[${e.index}]`),s());n(")")}(e,t);break;case 21:nn(e.body,t,!0,!1)}}function rn(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function sn(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];m(o)?t.push(o):on(o,t)}}function cn(e,t){const{push:n}=t;if(8===e.type)n("["),sn(e,t),n("]");else if(e.isStatic){n($e(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}function ln(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(const o of e.properties)ln("RestElement"===o.type?o.argument:o.value,t);break;case"ArrayPattern":e.elements.forEach((e=>{e&&ln(e,t)}));break;case"RestElement":ln(e.argument,t);break;case"AssignmentPattern":ln(e.left,t)}return t}const an=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed;function pn(e,t,n=!1,o=!1,r=Object.create(t.identifiers)){return e}const un=Yt(/^(if|else|else-if)$/,((e,t,n)=>fn(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),i=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(o)e.codegenNode=hn(t,i,n);else{const o=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);o.alternate=hn(t,i+e.branches.length-1,n)}}}))));function fn(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){t.exp=_e("true",!1,t.exp?t.exp.loc:e.loc)}if("if"===t.name){const r=dn(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const i=r[s];if(i&&3===i.type)n.removeNode(i);else{if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){n.removeNode();const r=dn(e,t);i.branches.push(r);const s=o&&o(i,r,!1);zt(r,n),s&&s(),n.currentNode=null}break}n.removeNode(i)}}}}function dn(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!Ke(e,"for")?e.children:[e],userKey:Ue(e,"key"),isTemplateIf:n}}function hn(e,t,n){return e.condition?Oe(e.condition,mn(e,t,n),Te(n.helper(F),['""',"true"])):mn(e,t,n)}function mn(e,t,n){const{helper:o}=n,r=Ne("key",_e(`${t}`,!1,ye,2)),{children:s}=e,i=s[0];if(1!==s.length||1!==i.type){if(1===s.length&&11===i.type){const e=i.codegenNode;return nt(e,r,n),e}{let t=64;return Se(n,o(M),Ee([r]),s,t+"",void 0,void 0,!0,!1,!1,e.loc)}}{const e=i.codegenNode,t=st(e);return 13===t.type&&it(t,n),nt(t,r,n),e}}const gn=Yt("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return yn(e,t,n,(t=>{const s=Te(o(z),[t.source]),i=Ze(e),c=Ke(e,"memo"),l=Ue(e,"key"),a=l&&(6===l.type?_e(l.value.content,!0):l.exp),p=l?Ne("key",a):null,u=4===t.source.type&&t.source.constType>0,f=u?64:l?128:256;return t.codegenNode=Se(n,o(M),void 0,s,f+"",void 0,void 0,!0,!u,!1,e.loc),()=>{let l;const{children:f}=t,d=1!==f.length||1!==f[0].type,h=qe(e)?e:i&&1===e.children.length&&qe(e.children[0])?e.children[0]:null;if(h?(l=h.codegenNode,i&&p&&nt(l,p,n)):d?l=Se(n,o(M),p?Ee([p]):void 0,e.children,"64",void 0,void 0,!0,void 0,!1):(l=f[0].codegenNode,i&&p&&nt(l,p,n),l.isBlock!==!u&&(l.isBlock?(r(L),r(Qe(n.inSSR,l.isComponent))):r(Xe(n.inSSR,l.isComponent))),l.isBlock=!u,l.isBlock?(o(L),o(Qe(n.inSSR,l.isComponent))):o(Xe(n.inSSR,l.isComponent))),c){const e=ke(_n(t.parseResult,[_e("_cached")]));e.body=Ie([xe(["const _memo = (",c.exp,")"]),xe(["if (_cached",...a?[" && _cached.key === ",a]:[],` && ${n.helperString(he)}(_cached, _memo)) return _cached`]),xe(["const _item = ",l]),_e("_item.memo = _memo"),_e("return _item")]),s.arguments.push(e,_e("_cache"),_e(String(n.cached++)))}else s.arguments.push(ke(_n(t.parseResult),l,!0))}}))}));function yn(e,t,n,o){if(!t.exp)return;const r=En(t.exp);if(!r)return;const{scopes:s}=n,{source:i,value:c,key:l,index:a}=r,p={type:11,loc:t.loc,source:i,valueAlias:c,keyAlias:l,objectIndexAlias:a,parseResult:r,children:Ze(e)?e.children:[e]};n.replaceNode(p),s.vFor++;const u=o&&o(p);return()=>{s.vFor--,u&&u()}}const vn=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Sn=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,bn=/^\(|\)$/g;function En(e,t){const n=e.loc,o=e.content,r=o.match(vn);if(!r)return;const[,s,i]=r,c={source:Nn(n,i.trim(),o.indexOf(i,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(bn,"").trim();const a=s.indexOf(l),p=l.match(Sn);if(p){l=l.replace(Sn,"").trim();const e=p[1].trim();let t;if(e&&(t=o.indexOf(e,a+l.length),c.key=Nn(n,e,t)),p[2]){const r=p[2].trim();r&&(c.index=Nn(n,r,o.indexOf(r,c.key?t+e.length:a+l.length)))}}return l&&(c.value=Nn(n,l,a)),c}function Nn(e,t,n){return _e(t,!1,je(e,n,t.length))}function _n({value:e,key:t,index:n},o=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||_e("_".repeat(t+1),!1)))}([e,t,n,...o])}const xn=_e("undefined",!1),Tn=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Ke(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},kn=(e,t,n)=>ke(e,t,!1,!0,t.length?t[0].loc:n);function On(e,t,n=kn){t.helper(pe);const{children:o,loc:r}=e,s=[],i=[];let c=t.scopes.vSlot>0||t.scopes.vFor>0;const l=Ke(e,"slot",!0);if(l){const{arg:e,exp:t}=l;e&&!Me(e)&&(c=!0),s.push(Ne(e||_e("default",!0),n(t,o,r)))}let a=!1,p=!1;const u=[],f=new Set;let d=0;for(let g=0;g<o.length;g++){const e=o[g];let r;if(!Ze(e)||!(r=Ke(e,"slot",!0))){3!==e.type&&u.push(e);continue}if(l)break;a=!0;const{children:h,loc:m}=e,{arg:y=_e("default",!0),exp:v}=r;let S;Me(y)?S=y?y.content:"default":c=!0;const b=n(v,h,m);let E,N,_;if(E=Ke(e,"if"))c=!0,i.push(Oe(E.exp,Cn(y,b,d++),xn));else if(N=Ke(e,/^else(-if)?$/,!0)){let e,t=g;for(;t--&&(e=o[t],3===e.type););if(e&&Ze(e)&&Ke(e,"if")){o.splice(g,1),g--;let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=N.exp?Oe(N.exp,Cn(y,b,d++),xn):Cn(y,b,d++)}}else if(_=Ke(e,"for")){c=!0;const e=_.parseResult||En(_.exp);e&&i.push(Te(t.helper(z),[e.source,ke(_n(e),Cn(y,b),!0)]))}else{if(S){if(f.has(S))continue;f.add(S),"default"===S&&(p=!0)}s.push(Ne(y,b))}}if(!l){const e=(e,o)=>{const s=n(e,o,r);return t.compatConfig&&(s.isNonScopedSlot=!0),Ne("default",s)};a?u.length&&u.some((e=>Mn(e)))&&(p||s.push(e(void 0,u))):s.push(e(void 0,o))}const h=c?2:In(e.children)?3:1;let m=Ee(s.concat(Ne("_",_e(h+"",!1))),r);return i.length&&(m=Te(t.helper(Z),[m,be(i)])),{slots:m,hasDynamicSlots:c}}function Cn(e,t,n){const o=[Ne("name",e),Ne("fn",t)];return null!=n&&o.push(Ne("key",_e(String(n),!0))),Ee(o)}function In(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||In(n.children))return!0;break;case 9:if(In(n.branches))return!0;break;case 10:case 11:if(In(n.children))return!0}}return!1}function Mn(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():Mn(e.content))}const Rn=new WeakMap,Pn=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType;let s=r?wn(e,t):`"${n}"`;const i=y(s)&&s.callee===K;let c,l,a,p,u,f,d=0,h=i||s===R||s===P||!r&&("svg"===n||"foreignObject"===n);if(o.length>0){const n=$n(e,t,void 0,r,i);c=n.props,d=n.patchFlag,u=n.dynamicPropNames;const o=n.directives;f=o&&o.length?be(o.map((e=>An(e,t)))):void 0,n.shouldUseBlock&&(h=!0)}if(e.children.length>0){s===w&&(h=!0,d|=1024);if(r&&s!==R&&s!==w){const{slots:n,hasDynamicSlots:o}=On(e,t);l=n,o&&(d|=1024)}else if(1===e.children.length&&s!==R){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===Ft(n,t)&&(d|=1),l=r||2===o?n:e.children}else l=e.children}0!==d&&(a=String(d),u&&u.length&&(p=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(u))),e.codegenNode=Se(t,s,c,l,a,p,f,!!h,!1,r,e.loc)};function wn(e,t,n=!1){let{tag:o}=e;const r=Dn(o),s=Ue(e,"is");if(s)if(r||at("COMPILER_IS_ON_ELEMENT",t)){const e=6===s.type?s.value&&_e(s.value.content,!0):s.exp;if(e)return Te(t.helper(K),[e])}else 6===s.type&&s.value.content.startsWith("vue:")&&(o=s.value.content.slice(4));const i=!r&&Ke(e,"is");if(i&&i.exp)return Te(t.helper(K),[i.exp]);const c=Pe(o)||t.isBuiltInComponent(o);return c?(n||t.helper(c),c):(t.helper(W),t.components.add(o),rt(o,"component"))}function $n(e,t,n=e.props,o,r,s=!1){const{tag:i,loc:c,children:l}=e;let a=[];const p=[],u=[],d=l.length>0;let h=!1,m=0,y=!1,b=!1,E=!1,N=!1,_=!1,x=!1;const T=[],k=e=>{a.length&&(p.push(Ee(Ln(a),c)),a=[]),e&&p.push(e)},O=({key:e,value:n})=>{if(Me(e)){const s=e.content,i=f(s);if(!i||o&&!r||"onclick"===s.toLowerCase()||"onUpdate:modelValue"===s||v(s)||(N=!0),i&&v(s)&&(x=!0),20===n.type||(4===n.type||8===n.type)&&Ft(n,t)>0)return;"ref"===s?y=!0:"class"===s?b=!0:"style"===s?E=!0:"key"===s||T.includes(s)||T.push(s),!o||"class"!==s&&"style"!==s||T.includes(s)||T.push(s)}else _=!0};for(let f=0;f<n.length;f++){const r=n[f];if(6===r.type){const{loc:e,name:n,value:o}=r;let s=!0;if("ref"===n&&(y=!0,t.scopes.vFor>0&&a.push(Ne(_e("ref_for",!0),_e("true")))),"is"===n&&(Dn(i)||o&&o.content.startsWith("vue:")||at("COMPILER_IS_ON_ELEMENT",t)))continue;a.push(Ne(_e(n,!0,je(e,0,n.length)),_e(o?o.content:"",s,o?o.loc:e)))}else{const{name:n,arg:l,exp:f,loc:m}=r,y="bind"===n,v="on"===n;if("slot"===n)continue;if("once"===n||"memo"===n)continue;if("is"===n||y&&Je(l,"is")&&(Dn(i)||at("COMPILER_IS_ON_ELEMENT",t)))continue;if(v&&s)continue;if((y&&Je(l,"key")||v&&d&&Je(l,"vue:before-update"))&&(h=!0),y&&Je(l,"ref")&&t.scopes.vFor>0&&a.push(Ne(_e("ref_for",!0),_e("true"))),!l&&(y||v)){if(_=!0,f)if(y){if(k(),at("COMPILER_V_BIND_OBJECT_ORDER",t)){p.unshift(f);continue}p.push(f)}else k({type:14,loc:m,callee:t.helper(oe),arguments:o?[f]:[f,"true"]});continue}const b=t.directiveTransforms[n];if(b){const{props:n,needRuntime:o}=b(r,e,t);!s&&n.forEach(O),v&&l&&!Me(l)?k(Ee(n,c)):a.push(...n),o&&(u.push(r),g(o)&&Rn.set(r,o))}else S(n)||(u.push(r),d&&(h=!0))}}let C;if(p.length?(k(),C=p.length>1?Te(t.helper(X),p,c):p[0]):a.length&&(C=Ee(Ln(a),c)),_?m|=16:(b&&!o&&(m|=2),E&&!o&&(m|=4),T.length&&(m|=8),N&&(m|=32)),h||0!==m&&32!==m||!(y||x||u.length>0)||(m|=512),!t.inSSR&&C)switch(C.type){case 15:let e=-1,n=-1,o=!1;for(let t=0;t<C.properties.length;t++){const r=C.properties[t].key;Me(r)?"class"===r.content?e=t:"style"===r.content&&(n=t):r.isHandlerKey||(o=!0)}const r=C.properties[e],s=C.properties[n];o?C=Te(t.helper(te),[C]):(r&&!Me(r.value)&&(r.value=Te(t.helper(Q),[r.value])),s&&(E||4===s.value.type&&"["===s.value.content.trim()[0]||17===s.value.type)&&(s.value=Te(t.helper(ee),[s.value])));break;case 14:break;default:C=Te(t.helper(te),[Te(t.helper(ne),[C])])}return{props:C,directives:u,patchFlag:m,dynamicPropNames:T,shouldUseBlock:h}}function Ln(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,i=t.get(s);i?("style"===s||"class"===s||f(s))&&Vn(i,r):(t.set(s,r),n.push(r))}return n}function Vn(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=be([e.value,t.value],e.loc)}function An(e,t){const n=[],o=Rn.get(e);o?n.push(t.helperString(o)):(t.helper(U),t.directives.add(e.name),n.push(rt(e.name,"directive")));const{loc:r}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=_e("true",!1,r);n.push(Ee(e.modifiers.map((e=>Ne(e,t))),r))}return be(n,e.loc)}function Dn(e){return"component"===e||"Component"===e}const Bn=(e,t)=>{if(qe(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=Fn(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r,"{}","undefined","true"];let c=2;s&&(i[2]=s,c=3),n.length&&(i[3]=ke([],n,!1,!1,o),c=4),t.scopeId&&!t.slotted&&(c=5),i.splice(c),e.codegenNode=Te(t.helper(Y),i,o)}};function Fn(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=N(t.name),r.push(t))):"bind"===t.name&&Je(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&Me(t.arg)&&(t.arg.content=N(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=$n(e,t,r,!1,!1);n=o}return{slotName:o,slotProps:n}}const jn=/^\s*([\w$_]+|(async\s*)?\([^)]*?\))\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,Hn=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:i}=e;let c;if(4===i.type)if(i.isStatic){let e=i.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`);c=_e(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?k(N(e)):`on:${e}`,!0,i.loc)}else c=xe([`${n.helperString(ie)}(`,i,")"]);else c=i,c.children.unshift(`${n.helperString(ie)}(`),c.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let a=n.cacheHandlers&&!l&&!n.inVOnce;if(l){const e=Fe(l.content),t=!(e||jn.test(l.content)),n=l.content.includes(";");(t||a&&e)&&(l=xe([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let p={props:[Ne(c,l||_e("() => {}",!1,r))]};return o&&(p=o(p)),a&&(p.props[0].value=n.cache(p.props[0].value)),p.props.forEach((e=>e.key.isHandlerKey=!0)),p},Wn=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,i=e.arg;return 4!==i.type?(i.children.unshift("("),i.children.push(') || ""')):i.isStatic||(i.content=`${i.content} || ""`),r.includes("camel")&&(4===i.type?i.content=i.isStatic?N(i.content):`${n.helperString(re)}(${i.content})`:(i.children.unshift(`${n.helperString(re)}(`),i.children.push(")"))),n.inSSR||(r.includes("prop")&&Kn(i,"."),r.includes("attr")&&Kn(i,"^")),!o||4===o.type&&!o.content.trim()?{props:[Ne(i,_e("",!0,s))]}:{props:[Ne(i,o)]}},Kn=(e,t)=>{4===e.type?e.content=e.isStatic?t+e.content:`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},Un=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(ze(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!ze(s)){o=void 0;break}o||(o=n[e]=xe([t],t.loc)),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const o=n[e];if(ze(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==Ft(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:Te(t.helper(j),r)}}}}},Jn=new WeakSet,Gn=(e,t)=>{if(1===e.type&&Ke(e,"once",!0)){if(Jn.has(e)||t.inVOnce)return;return Jn.add(e),t.inVOnce=!0,t.helper(ce),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},zn=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return Yn();const s=o.loc.source,i=4===o.type?o.content:s,c=n.bindingMetadata[s];if("props"===c||"props-aliased"===c)return Yn();if(!i.trim()||!Fe(i))return Yn();const l=r||_e("modelValue",!0),a=r?Me(r)?`onUpdate:${N(r.content)}`:xe(['"onUpdate:" + ',r]):"onUpdate:modelValue";let p;p=xe([`${n.isTS?"($event: any)":"$event"} => ((`,o,") = $event)"]);const u=[Ne(l,e.exp),Ne(a,p)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>($e(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?Me(r)?`${r.content}Modifiers`:xe([r,' + "Modifiers"']):"modelModifiers";u.push(Ne(n,_e(`{ ${t} }`,!1,e.loc,2)))}return Yn(u)};function Yn(e=[]){return{props:e}}const Zn=/[\w).+\-_$\]]/,qn=(e,t)=>{at("COMPILER_FILTER",t)&&(5===e.type&&Xn(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&Xn(e.exp,t)})))};function Xn(e,t){if(4===e.type)Qn(e,t);else for(let n=0;n<e.children.length;n++){const o=e.children[n];"object"==typeof o&&(4===o.type?Qn(o,t):8===o.type?Xn(e,t):5===o.type&&Xn(o.content,t))}}function Qn(e,t){const n=e.content;let o,r,s,i,c=!1,l=!1,a=!1,p=!1,u=0,f=0,d=0,h=0,m=[];for(s=0;s<n.length;s++)if(r=o,o=n.charCodeAt(s),c)39===o&&92!==r&&(c=!1);else if(l)34===o&&92!==r&&(l=!1);else if(a)96===o&&92!==r&&(a=!1);else if(p)47===o&&92!==r&&(p=!1);else if(124!==o||124===n.charCodeAt(s+1)||124===n.charCodeAt(s-1)||u||f||d){switch(o){case 34:l=!0;break;case 39:c=!0;break;case 96:a=!0;break;case 40:d++;break;case 41:d--;break;case 91:f++;break;case 93:f--;break;case 123:u++;break;case 125:u--}if(47===o){let e,t=s-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&Zn.test(e)||(p=!0)}}else void 0===i?(h=s+1,i=n.slice(0,s).trim()):g();function g(){m.push(n.slice(h,s).trim()),h=s+1}if(void 0===i?i=n.slice(0,s).trim():0!==h&&g(),m.length){for(s=0;s<m.length;s++)i=eo(i,m[s],t);e.content=i}}function eo(e,t,n){n.helper(J);const o=t.indexOf("(");if(o<0)return n.filters.add(t),`${rt(t,"filter")}(${e})`;{const r=t.slice(0,o),s=t.slice(o+1);return n.filters.add(r),`${rt(r,"filter")}(${e}${")"!==s?","+s:s}`}}const to=new WeakSet,no=(e,t)=>{if(1===e.type){const n=Ke(e,"memo");if(!n||to.has(e))return;return to.add(e),()=>{const o=e.codegenNode||t.currentNode.codegenNode;o&&13===o.type&&(1!==e.tagType&&it(o,t),e.codegenNode=Te(t.helper(de),[n.exp,ke(void 0,o),"_cache",String(t.cached++)]))}}};function oo(e){return[[Gn,un,no,gn,qn,Bn,Pn,Tn,Un],{on:Hn,bind:Wn,model:zn}]}function ro(e,t={}){const n=t.onError||O,o="module"===t.mode;!0===t.prefixIdentifiers?n(I(47)):o&&n(I(48));t.cacheHandlers&&n(I(49)),t.scopeId&&!o&&n(I(50));const r=m(e)?ht(e,t):e,[s,i]=oo();return Gt(r,d({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:d({},i,t.directiveTransforms||{})})),Qt(r,d({},t,{prefixIdentifiers:false}))}const so=()=>({props:[]}),io=Symbol(""),co=Symbol(""),lo=Symbol(""),ao=Symbol(""),po=Symbol(""),uo=Symbol(""),fo=Symbol(""),ho=Symbol(""),mo=Symbol(""),go=Symbol("");let yo;ge({[io]:"vModelRadio",[co]:"vModelCheckbox",[lo]:"vModelText",[ao]:"vModelSelect",[po]:"vModelDynamic",[uo]:"withModifiers",[fo]:"withKeys",[ho]:"vShow",[mo]:"Transition",[go]:"TransitionGroup"});const vo=t("style,iframe,script,noscript",!0),So={isVoidTag:c,isNativeTag:e=>s(e)||i(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return yo||(yo=document.createElement("div")),t?(yo.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,yo.children[0].getAttribute("foo")):(yo.innerHTML=e,yo.textContent)},isBuiltInComponent:e=>Re(e,"Transition")?mo:Re(e,"TransitionGroup")?go:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(vo(e))return 2}return 0}},bo=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:_e("style",!0,t.loc),exp:Eo(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},Eo=(e,t)=>{const s=function(e){const t={};return e.replace(r,"").split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return _e(JSON.stringify(s),!1,t,3)};function No(e,t){return I(e,t)}const _o=t("passive,once,capture"),xo=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),To=t("left,right"),ko=t("onkeyup,onkeydown,onkeypress",!0),Oo=(e,t)=>Me(e)&&"onclick"===e.content.toLowerCase()?_e(t,!0):4!==e.type?xe(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,Co=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Io=[bo],Mo={cloak:so,html:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[Ne(_e("innerHTML",!0,r),o||_e("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[Ne(_e("textContent",!0),o?Ft(o,n)>0?o:Te(n.helperString(q),[o],r):_e("",!0))]}},model:(e,t,n)=>{const o=zn(e,t,n);if(!o.props.length||1===t.tagType)return o;const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let e=lo,i=!1;if("input"===r||s){const n=Ue(t,"type");if(n){if(7===n.type)e=po;else if(n.value)switch(n.value.content){case"radio":e=io;break;case"checkbox":e=co;break;case"file":i=!0}}else Ge(t)&&(e=po)}else"select"===r&&(e=ao);i||(o.needRuntime=n.helper(e))}return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>Hn(e,t,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:i,nonKeyModifiers:c,eventOptionModifiers:l}=((e,t,n,o)=>{const r=[],s=[],i=[];for(let c=0;c<t.length;c++){const o=t[c];"native"===o&&pt("COMPILER_V_ON_NATIVE",n)||_o(o)?i.push(o):To(o)?Me(e)?ko(e.content)?r.push(o):s.push(o):(r.push(o),s.push(o)):xo(o)?s.push(o):r.push(o)}return{keyModifiers:r,nonKeyModifiers:s,eventOptionModifiers:i}})(r,o,n);if(c.includes("right")&&(r=Oo(r,"onContextmenu")),c.includes("middle")&&(r=Oo(r,"onMouseup")),c.length&&(s=Te(n.helper(uo),[s,JSON.stringify(c)])),!i.length||Me(r)&&!ko(r.content)||(s=Te(n.helper(fo),[s,JSON.stringify(i)])),l.length){const e=l.map(T).join("");r=Me(r)?_e(`${r.content}${e}`,!0):xe(["(",r,`) + "${e}"`])}return{props:[Ne(r,s)]}})),show:(e,t,n)=>({props:[],needRuntime:n.helper(ho)})};return e.BASE_TRANSITION=$,e.CAMELIZE=re,e.CAPITALIZE=se,e.CREATE_BLOCK=V,e.CREATE_COMMENT=F,e.CREATE_ELEMENT_BLOCK=A,e.CREATE_ELEMENT_VNODE=B,e.CREATE_SLOTS=Z,e.CREATE_STATIC=H,e.CREATE_TEXT=j,e.CREATE_VNODE=D,e.DOMDirectiveTransforms=Mo,e.DOMNodeTransforms=Io,e.FRAGMENT=M,e.GUARD_REACTIVE_PROPS=ne,e.IS_MEMO_SAME=he,e.IS_REF=fe,e.KEEP_ALIVE=w,e.MERGE_PROPS=X,e.NORMALIZE_CLASS=Q,e.NORMALIZE_PROPS=te,e.NORMALIZE_STYLE=ee,e.OPEN_BLOCK=L,e.POP_SCOPE_ID=ae,e.PUSH_SCOPE_ID=le,e.RENDER_LIST=z,e.RENDER_SLOT=Y,e.RESOLVE_COMPONENT=W,e.RESOLVE_DIRECTIVE=U,e.RESOLVE_DYNAMIC_COMPONENT=K,e.RESOLVE_FILTER=J,e.SET_BLOCK_TRACKING=ce,e.SUSPENSE=P,e.TELEPORT=R,e.TO_DISPLAY_STRING=q,e.TO_HANDLERS=oe,e.TO_HANDLER_KEY=ie,e.TRANSITION=mo,e.TRANSITION_GROUP=go,e.UNREF=ue,e.V_MODEL_CHECKBOX=co,e.V_MODEL_DYNAMIC=po,e.V_MODEL_RADIO=io,e.V_MODEL_SELECT=ao,e.V_MODEL_TEXT=lo,e.V_ON_WITH_KEYS=fo,e.V_ON_WITH_MODIFIERS=uo,e.V_SHOW=ho,e.WITH_CTX=pe,e.WITH_DIRECTIVES=G,e.WITH_MEMO=de,e.advancePositionWithClone=He,e.advancePositionWithMutation=We,e.assert=function(e,t){if(!e)throw new Error(t||"unexpected compiler condition")},e.baseCompile=ro,e.baseParse=ht,e.buildDirectiveArgs=An,e.buildProps=$n,e.buildSlots=On,e.checkCompatEnabled=pt,e.compile=function(e,t={}){return ro(e,d({},So,t,{nodeTransforms:[Co,...Io,...t.nodeTransforms||[]],directiveTransforms:d({},Mo,t.directiveTransforms||{}),transformHoist:null}))},e.createArrayExpression=be,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:ye}},e.createBlockStatement=Ie,e.createCacheExpression=Ce,e.createCallExpression=Te,e.createCompilerError=I,e.createCompoundExpression=xe,e.createConditionalExpression=Oe,e.createDOMCompilerError=No,e.createForLoopParams=_n,e.createFunctionExpression=ke,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:ye}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:m(e)?_e(e,!1,t):e}},e.createObjectExpression=Ee,e.createObjectProperty=Ne,e.createReturnStatement=function(e){return{type:26,returns:e,loc:ye}},e.createRoot=ve,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:ye}},e.createSimpleExpression=_e,e.createStructuralDirectiveTransform=Yt,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:ye}},e.createTransformContext=Jt,e.createVNodeCall=Se,e.extractIdentifiers=ln,e.findDir=Ke,e.findProp=Ue,e.generate=Qt,e.generateCodeFrame=function(e,t=0,n=e.length){let o=e.split(/(\r?\n)/);const r=o.filter(((e,t)=>t%2==1));o=o.filter(((e,t)=>t%2==0));let s=0;const i=[];for(let c=0;c<o.length;c++)if(s+=o[c].length+(r[c]&&r[c].length||0),s>=t){for(let e=c-2;e<=c+2||n>s;e++){if(e<0||e>=o.length)continue;const l=e+1;i.push(`${l}${" ".repeat(Math.max(3-String(l).length,0))}|  ${o[e]}`);const a=o[e].length,p=r[e]&&r[e].length||0;if(e===c){const e=t-(s-(a+p)),o=Math.max(1,n>s?a-e:n-t);i.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>c){if(n>s){const e=Math.max(Math.min(n-s,a),1);i.push("   |  "+"^".repeat(e))}s+=a+p}}break}return i.join("\n")},e.getBaseTransformPreset=oo,e.getConstantType=Ft,e.getInnerRange=je,e.getMemoedVNodeCall=st,e.getVNodeBlockHelper=Qe,e.getVNodeHelper=Xe,e.hasDynamicKeyVBind=Ge,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let o=0;o<t.props.length;o++){const r=t.props[o];if(7===r.type&&(e(r.arg,n)||e(r.exp,n)))return!0}return t.children.some((t=>e(t,n)));case 11:return!!e(t.source,n)||t.children.some((t=>e(t,n)));case 9:return t.branches.some((t=>e(t,n)));case 10:return!!e(t.condition,n)||t.children.some((t=>e(t,n)));case 4:return!t.isStatic&&$e(t.content)&&!!n[t.content];case 8:return t.children.some((t=>y(t)&&e(t,n)));case 5:case 12:return e(t.content,n);default:return!1}},e.helperNameMap=me,e.injectProp=nt,e.isBuiltInType=Re,e.isCoreComponent=Pe,e.isFunctionType=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),e.isInDestructureAssignment=function(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){const n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1},e.isMemberExpression=Fe,e.isMemberExpressionBrowser=De,e.isMemberExpressionNode=Be,e.isReferencedIdentifier=function(e,t,n){return!1},e.isSimpleIdentifier=$e,e.isSlotOutlet=qe,e.isStaticArgOf=Je,e.isStaticExp=Me,e.isStaticProperty=an,e.isStaticPropertyKey=(e,t)=>an(t)&&t.key===e,e.isTemplateNode=Ze,e.isText=ze,e.isVSlot=Ye,e.locStub=ye,e.makeBlock=it,e.noopDirectiveTransform=so,e.parse=function(e,t={}){return ht(e,d({},So,t))},e.parserOptions=So,e.processExpression=pn,e.processFor=yn,e.processIf=fn,e.processSlotOutlet=Fn,e.registerRuntimeHelpers=ge,e.resolveComponentType=wn,e.stringifyExpression=function e(t){return m(t)?t:4===t.type?t.content:t.children.map(e).join("")},e.toValidAssetId=rt,e.trackSlotScopes=Tn,e.trackVForSlotScopes=(e,t)=>{let n;if(Ze(e)&&e.props.some(Ye)&&(n=Ke(e,"for"))){const e=n.parseResult=En(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:i}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&i(n),o&&i(o),r&&i(r)}}}},e.transform=Gt,e.transformBind=Wn,e.transformElement=Pn,e.transformExpression=(e,t)=>{if(5===e.type)e.content=pn(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=pn(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=pn(n,t))}}},e.transformModel=zn,e.transformOn=Hn,e.transformStyle=bo,e.traverseNode=zt,e.walkBlockDeclarations=function(e,t){for(const n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(const e of n.declarations)for(const n of ln(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}},e.walkFunctionParams=function(e,t){for(const n of e.params)for(const e of ln(n))t(e)},e.walkIdentifiers=function(e,t,n=!1,o=[],r=Object.create(null)){},e.warnDeprecation=function(e,t,n,...o){if("suppress-warning"===lt(e,t))return;const{message:r,link:s}=ct[e],i=`(deprecation ${e}) ${"function"==typeof r?r(...o):r}${s?`\n  Details: ${s}`:""}`,c=new SyntaxError(i);c.code=e,n&&(c.loc=n),t.onWarn(c)},Object.defineProperty(e,"__esModule",{value:!0}),e}({});

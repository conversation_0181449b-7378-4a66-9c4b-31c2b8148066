<template>
  <view class="life-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-life">
      <text class="header-title">生活助手</text>
      <text class="header-subtitle">便民服务，贴心生活</text>
    </view>
    
    <!-- 工具分类 -->
    <view class="tools-section">
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">日常计算</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card" @click="goToTool('calculator')">
            <view class="tool-icon" style="background: #f59e0b;">
              <text class="icon">🧮</text>
            </view>
            <text class="tool-title">科学计算器</text>
            <text class="tool-desc">多功能计算工具</text>
          </view>
          
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #8b5cf6;">
              <text class="icon">📏</text>
            </view>
            <text class="tool-title">单位转换</text>
            <text class="tool-desc">长度重量转换</text>
          </view>
        </view>
      </view>
      
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">健康生活</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #10b981;">
              <text class="icon">💪</text>
            </view>
            <text class="tool-title">BMI计算</text>
            <text class="tool-desc">身体质量指数</text>
          </view>
          
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #ef4444;">
              <text class="icon">❤️</text>
            </view>
            <text class="tool-title">健康记录</text>
            <text class="tool-desc">健康数据管理</text>
          </view>
        </view>
      </view>
      
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">出行助手</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #06b6d4;">
              <text class="icon">🌤️</text>
            </view>
            <text class="tool-title">天气查询</text>
            <text class="tool-desc">实时天气预报</text>
          </view>
          
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #ec4899;">
              <text class="icon">⛽</text>
            </view>
            <text class="tool-title">油价查询</text>
            <text class="tool-desc">实时油价信息</text>
          </view>
        </view>
      </view>
      
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">实用工具</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #6366f1;">
              <text class="icon">🔍</text>
            </view>
            <text class="tool-title">二维码</text>
            <text class="tool-desc">生成识别二维码</text>
          </view>
          
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #f59e0b;">
              <text class="icon">🎨</text>
            </view>
            <text class="tool-title">颜色工具</text>
            <text class="tool-desc">颜色选择器</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 生活贴士 -->
    <view class="tips-section">
      <view class="section-header">
        <text class="section-title">生活贴士</text>
      </view>
      
      <view class="tips-list">
        <view class="tip-item" v-for="(tip, index) in lifeTips" :key="index">
          <view class="tip-icon">
            <text>{{ tip.icon }}</text>
          </view>
          <view class="tip-content">
            <text class="tip-title">{{ tip.title }}</text>
            <text class="tip-desc">{{ tip.desc }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      lifeTips: [
        {
          icon: '💡',
          title: '节能小贴士',
          desc: '合理使用空调，温度设置在26°C最节能'
        },
        {
          icon: '🥗',
          title: '健康饮食',
          desc: '每天至少吃5种不同颜色的蔬菜水果'
        },
        {
          icon: '💧',
          title: '多喝水',
          desc: '成人每天应该喝8杯水，约2000毫升'
        },
        {
          icon: '🚶',
          title: '适量运动',
          desc: '每天步行30分钟，有助于身体健康'
        }
      ]
    }
  },
  
  onLoad() {
    this.loadLifeStats()
  },
  
  onShow() {
    this.loadLifeStats()
  },
  
  methods: {
    // 跳转到工具
    goToTool(tool) {
      // 记录工具使用
      this.recordToolUsage(tool)
      
      const toolRoutes = {
        calculator: '/pages/life/calculator/index'
      }
      
      const route = toolRoutes[tool]
      if (route) {
        uni.navigateTo({
          url: route
        })
      } else {
        uni.showToast({
          title: '功能即将推出',
          icon: 'none'
        })
      }
    },
    
    // 加载生活统计
    loadLifeStats() {
      // 可以在这里加载用户的生活助手使用统计
    },
    
    // 记录工具使用
    recordToolUsage(tool) {
      // 触发使用工具任务
      this.$store.dispatch('redpacket/checkUserBehavior', 'use_any_tool')
    }
  }
}
</script>

<style lang="scss" scoped>
.life-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 工具区域 */
.tools-section {
  padding: 32rpx;
}

.category-section {
  margin-bottom: 48rpx;
}

.category-header {
  margin-bottom: 24rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.tool-card {
  background: white;
  border-radius: 12px;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  cursor: pointer;
}

.tool-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.tool-card.coming-soon {
  opacity: 0.6;
  cursor: not-allowed;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}

.tool-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
  display: block;
}

.tool-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
  display: block;
}

/* 生活贴士 */
.tips-section {
  padding: 0 32rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.tips-list {
  background: white;
  border-radius: 12px;
  padding: 32rpx;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(245, 158, 11, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
  display: block;
}

@media (max-width: 500rpx) {
  .tools-grid {
    grid-template-columns: 1fr;
  }
}
</style>

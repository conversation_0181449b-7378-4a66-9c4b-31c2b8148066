/**
 * 这里是uni-app内置的常用样式变量
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #6366f1;
$uni-color-success: #10b981;
$uni-color-warning: #f59e0b;
$uni-color-error: #dc2626;

/* 文字基本颜色 */
$uni-text-color: #333;
$uni-text-color-inverse: #fff;
$uni-text-color-grey: #999;
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #f8f9fa;
$uni-bg-color-grey: #f5f5f5;
$uni-bg-color-hover: #f1f1f1;
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

/* 边框颜色 */
$uni-border-color: #e5e7eb;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10rpx;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3;

/* 文章场景相关 */
$uni-color-title: #2c405a;
$uni-color-subtitle: #555555;
$uni-color-paragraph: #3f536e;

/* CSS变量定义 */
:root {
  --primary-color: #{$uni-color-primary};
  --secondary-color: #ec4899;
  --success-color: #{$uni-color-success};
  --warning-color: #{$uni-color-warning};
  --danger-color: #{$uni-color-error};
  
  --text-color: #{$uni-text-color};
  --text-light: #{$uni-text-color-grey};
  --text-placeholder: #{$uni-text-color-placeholder};
  
  --bg-color: #{$uni-bg-color};
  --card-bg: #ffffff;
  --border-color: #{$uni-border-color};
  
  --radius: #{$uni-border-radius-lg};
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.gradient-entertainment {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.gradient-work {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.gradient-life {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.gradient-redpacket {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

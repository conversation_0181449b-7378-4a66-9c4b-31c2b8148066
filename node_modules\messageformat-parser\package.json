{"name": "messageformat-parser", "version": "4.1.3", "description": "A PEG.js parser for ICU MessageFormat strings", "keywords": ["icu", "messageformat", "parser"], "contributors": ["<PERSON> <alex<PERSON><EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://messageformat.github.io/", "repository": {"type": "git", "url": "https://github.com/messageformat/messageformat.git", "directory": "packages/parser"}, "main": "parser.js", "files": ["parser.js", "codemod-fix-backslash-escapes.js"], "eslintConfig": {"env": {"commonjs": true, "es6": true}, "overrides": [{"files": ["test.js"], "env": {"mocha": true}, "rules": {"no-unused-vars": 0}}]}, "scripts": {"build": "pegjs parser.pegjs", "prepublishOnly": "npm test", "test": "mocha"}, "gitHead": "dd10833d8d39155b005ea2ba6ec7a87f4daf0cd2"}
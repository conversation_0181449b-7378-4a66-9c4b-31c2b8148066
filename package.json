{"name": "mini-utils", "version": "1.0.0", "description": "多功能工具箱小程序", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "uni build -p app-plus", "build:custom": "uni build -p", "build:h5": "uni build -p h5", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-qq": "uni build -p mp-qq", "build:quickapp-native": "uni build -p quickapp-native", "build:quickapp-webview": "uni build -p quickapp-webview", "dev:app-plus": "uni -p app-plus", "dev:custom": "uni -p", "dev:h5": "uni -p h5", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-qq": "uni -p mp-qq", "dev:quickapp-native": "uni -p quickapp-native", "dev:quickapp-webview": "uni -p quickapp-webview", "info": "node node_modules/@dcloudio/vite-plugin-uni/dist/cli/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "uni build -p app-plus && adb install -r dist/build/app-plus/android_debug.apk", "test:h5": "uni build -p h5 && npx serve dist/build/h5", "test:ios": "uni build -p app-plus", "test:mp-weixin": "uni build -p mp-weixin"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-app-plus": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-components": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-h5": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-quickapp-native": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-3060920230817001", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-i18n": "^9.2.2"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-3060920230817001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-3060920230817001", "@vue/tsconfig": "^0.4.0", "sass": "^1.64.1", "typescript": "^5.1.6", "vite": "^4.4.6"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "keywords": ["uniapp", "小程序", "工具箱", "红包", "游戏"], "author": "MiniMax Agent", "license": "MIT"}
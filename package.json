{"name": "mini-utils", "version": "1.0.0", "description": "多功能工具箱小程序", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build"}, "dependencies": {"@dcloudio/uni-app": "2.0.2-3081220230817001", "@dcloudio/uni-cli-i18n": "^2.0.2-4060620250520001", "@dcloudio/uni-cli-shared": "^2.0.2-4060620250520001", "@dcloudio/uni-h5": "2.0.2-3081220230817001", "@dcloudio/uni-mp-weixin": "2.0.2-3081220230817001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-4060620250520001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-4060620250520001", "core-js": "^3.6.5", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/uni-template-compiler": "2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni": "2.0.2-3081220230817001", "@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-service": "~4.4.0", "cross-env": "^7.0.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}
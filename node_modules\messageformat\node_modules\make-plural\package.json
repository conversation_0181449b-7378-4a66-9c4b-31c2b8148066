{"name": "make-plural", "version": "4.3.0", "description": "Translates Unicode CLDR pluralization rules to executable JavaScript", "keywords": ["unicode", "cldr", "i18n", "internationalization", "pluralization"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "ISC", "homepage": "https://github.com/eemeli/make-plural#readme", "repository": "eemeli/make-plural", "bugs": {"url": "https://github.com/eemeli/make-plural/issues"}, "files": ["bin/", "data/", "es6/", "make-plural.*", "umd/"], "bin": {"make-plural": "./bin/make-plural"}, "main": "umd/plurals", "scripts": {"lint": "standard 'src/*.js'", "test": "make test", "version": "git add -f $FILES"}, "devDependencies": {"@babel/cli": "^7.1.2", "@babel/core": "^7.1.2", "@babel/preset-env": "^7.1.0", "babel-plugin-add-module-exports": "^1.0.0", "babelify": "^10.0.0", "browserify": "^16.2.3", "cldr-core": "^34.0.0", "expect.js": "*", "http-server": "^0.11.1", "mocha": "^5.2.0", "standard": "^12.0.1", "uglify-js": "^3.4.9"}, "optionalDependencies": {"minimist": "^1.2.0"}}
# 音效文件说明

本目录包含应用中使用的各种音效文件。

## 文件列表

### 红包相关音效
- `redpacket_open.mp3` - 红包开启音效
- `coin_drop.mp3` - 金币掉落音效  
- `success.mp3` - 成功提示音效
- `pop.mp3` - 弹窗出现音效

### 游戏音效
- `chess_move.mp3` - 象棋移动音效
- `gobang_place.mp3` - 五子棋落子音效
- `game_win.mp3` - 游戏获胜音效
- `game_lose.mp3` - 游戏失败音效

### 系统音效
- `click.mp3` - 点击音效
- `notification.mp3` - 通知音效
- `error.mp3` - 错误提示音效

## 音效格式要求
- 格式：MP3
- 采样率：44.1kHz
- 比特率：128kbps
- 时长：建议不超过3秒

## 使用说明
音效文件通过 `uni.createInnerAudioContext()` API 播放，支持预加载和音效池管理。

## 版权说明
所有音效文件仅供学习和演示使用，请勿用于商业用途。

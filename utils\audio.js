/**
 * 音效管理工具类
 * 提供音效播放、管理和控制功能
 */

export class AudioManager {
  constructor() {
    this.audioPool = new Map() // 音效池
    this.isEnabled = true // 是否启用音效
    this.volume = 0.8 // 全局音量
    this.maxConcurrent = 5 // 最大并发播放数
    this.currentPlaying = new Set() // 当前播放的音效
    
    this.loadSettings()
    this.preloadAudio()
  }
  
  /**
   * 加载音效设置
   */
  loadSettings() {
    const settings = uni.getStorageSync('audio_settings') || {}
    this.isEnabled = settings.enabled !== false
    this.volume = settings.volume || 0.8
  }
  
  /**
   * 保存音效设置
   */
  saveSettings() {
    const settings = {
      enabled: this.isEnabled,
      volume: this.volume
    }
    uni.setStorageSync('audio_settings', settings)
  }
  
  /**
   * 预加载音效文件
   */
  preloadAudio() {
    const audioFiles = [
      'redpacket_open',
      'coin_drop',
      'success',
      'pop',
      'chess_move',
      'gobang_place',
      'game_win',
      'game_lose',
      'click',
      'notification',
      'error'
    ]
    
    audioFiles.forEach(fileName => {
      this.loadAudio(fileName)
    })
  }
  
  /**
   * 加载音效文件
   * @param {string} name - 音效名称
   * @param {string} path - 音效路径（可选）
   */
  loadAudio(name, path = null) {
    if (this.audioPool.has(name)) {
      return this.audioPool.get(name)
    }
    
    const audioPath = path || `/static/audio/${name}.mp3`
    
    try {
      const audio = uni.createInnerAudioContext()
      audio.src = audioPath
      audio.volume = this.volume
      audio.preload = true
      
      // 监听音效事件
      audio.onEnded(() => {
        this.currentPlaying.delete(name)
      })
      
      audio.onError((err) => {
        console.warn(`音效加载失败: ${name}`, err)
        this.audioPool.delete(name)
      })
      
      this.audioPool.set(name, audio)
      return audio
      
    } catch (error) {
      console.warn(`音效创建失败: ${name}`, error)
      return null
    }
  }
  
  /**
   * 播放音效
   * @param {string} name - 音效名称
   * @param {Object} options - 播放选项
   */
  play(name, options = {}) {
    if (!this.isEnabled) return
    
    // 检查并发播放限制
    if (this.currentPlaying.size >= this.maxConcurrent) {
      console.warn('音效播放达到并发限制')
      return
    }
    
    let audio = this.audioPool.get(name)
    
    // 如果音效不存在，尝试加载
    if (!audio) {
      audio = this.loadAudio(name, options.path)
      if (!audio) return
    }
    
    try {
      // 设置音效属性
      audio.volume = (options.volume || 1) * this.volume
      audio.loop = options.loop || false
      audio.startTime = options.startTime || 0
      
      // 播放音效
      audio.play()
      this.currentPlaying.add(name)
      
      // 如果设置了播放时长，自动停止
      if (options.duration) {
        setTimeout(() => {
          this.stop(name)
        }, options.duration)
      }
      
    } catch (error) {
      console.warn(`音效播放失败: ${name}`, error)
    }
  }
  
  /**
   * 停止音效
   * @param {string} name - 音效名称
   */
  stop(name) {
    const audio = this.audioPool.get(name)
    if (audio) {
      try {
        audio.stop()
        this.currentPlaying.delete(name)
      } catch (error) {
        console.warn(`音效停止失败: ${name}`, error)
      }
    }
  }
  
  /**
   * 暂停音效
   * @param {string} name - 音效名称
   */
  pause(name) {
    const audio = this.audioPool.get(name)
    if (audio) {
      try {
        audio.pause()
      } catch (error) {
        console.warn(`音效暂停失败: ${name}`, error)
      }
    }
  }
  
  /**
   * 停止所有音效
   */
  stopAll() {
    this.currentPlaying.forEach(name => {
      this.stop(name)
    })
    this.currentPlaying.clear()
  }
  
  /**
   * 设置全局音量
   * @param {number} volume - 音量 (0-1)
   */
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume))
    
    // 更新所有音效的音量
    this.audioPool.forEach(audio => {
      audio.volume = this.volume
    })
    
    this.saveSettings()
  }
  
  /**
   * 启用/禁用音效
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
    
    if (!enabled) {
      this.stopAll()
    }
    
    this.saveSettings()
  }
  
  /**
   * 获取音效状态
   * @param {string} name - 音效名称
   * @returns {Object} 音效状态
   */
  getStatus(name) {
    const audio = this.audioPool.get(name)
    if (!audio) {
      return { exists: false }
    }
    
    return {
      exists: true,
      playing: this.currentPlaying.has(name),
      volume: audio.volume,
      duration: audio.duration,
      currentTime: audio.currentTime
    }
  }
  
  /**
   * 销毁音效
   * @param {string} name - 音效名称
   */
  destroy(name) {
    const audio = this.audioPool.get(name)
    if (audio) {
      try {
        audio.destroy()
        this.audioPool.delete(name)
        this.currentPlaying.delete(name)
      } catch (error) {
        console.warn(`音效销毁失败: ${name}`, error)
      }
    }
  }
  
  /**
   * 销毁所有音效
   */
  destroyAll() {
    this.audioPool.forEach((audio, name) => {
      this.destroy(name)
    })
  }
  
  /**
   * 获取音效管理器状态
   * @returns {Object} 状态信息
   */
  getManagerStatus() {
    return {
      enabled: this.isEnabled,
      volume: this.volume,
      loadedCount: this.audioPool.size,
      playingCount: this.currentPlaying.size,
      maxConcurrent: this.maxConcurrent,
      loadedAudios: Array.from(this.audioPool.keys()),
      playingAudios: Array.from(this.currentPlaying)
    }
  }
}

/**
 * 音效预设配置
 */
export const AudioPresets = {
  // 红包音效
  redPacket: {
    open: { name: 'redpacket_open', volume: 0.8 },
    claim: { name: 'coin_drop', volume: 0.7 },
    success: { name: 'success', volume: 0.6 },
    pop: { name: 'pop', volume: 0.5 }
  },
  
  // 游戏音效
  game: {
    move: { name: 'chess_move', volume: 0.6 },
    place: { name: 'gobang_place', volume: 0.6 },
    win: { name: 'game_win', volume: 0.8 },
    lose: { name: 'game_lose', volume: 0.7 }
  },
  
  // 系统音效
  system: {
    click: { name: 'click', volume: 0.4 },
    notification: { name: 'notification', volume: 0.7 },
    error: { name: 'error', volume: 0.6 }
  }
}

/**
 * 全局音效管理器实例
 */
export const audioManager = new AudioManager()

/**
 * 便捷的音效播放函数
 */
export const playSound = {
  // 红包相关
  redPacketOpen: () => audioManager.play('redpacket_open'),
  coinDrop: () => audioManager.play('coin_drop'),
  success: () => audioManager.play('success'),
  pop: () => audioManager.play('pop'),
  
  // 游戏相关
  chessMove: () => audioManager.play('chess_move'),
  gobangPlace: () => audioManager.play('gobang_place'),
  gameWin: () => audioManager.play('game_win'),
  gameLose: () => audioManager.play('game_lose'),
  
  // 系统相关
  click: () => audioManager.play('click'),
  notification: () => audioManager.play('notification'),
  error: () => audioManager.play('error'),
  
  // 自定义播放
  custom: (name, options) => audioManager.play(name, options)
}

/**
 * 音效工具函数
 */
export const AudioUtils = {
  /**
   * 检查音效支持
   * @returns {boolean} 是否支持音效
   */
  isSupported() {
    try {
      const audio = uni.createInnerAudioContext()
      audio.destroy()
      return true
    } catch (error) {
      return false
    }
  },
  
  /**
   * 获取音效格式支持
   * @returns {Object} 支持的格式
   */
  getSupportedFormats() {
    return {
      mp3: true,
      wav: true,
      m4a: true,
      aac: false // 部分平台支持
    }
  },
  
  /**
   * 创建音效序列
   * @param {Array} sequence - 音效序列
   * @param {number} interval - 播放间隔(ms)
   */
  playSequence(sequence, interval = 200) {
    sequence.forEach((sound, index) => {
      setTimeout(() => {
        if (typeof sound === 'string') {
          audioManager.play(sound)
        } else {
          audioManager.play(sound.name, sound.options)
        }
      }, index * interval)
    })
  },
  
  /**
   * 创建音效淡入效果
   * @param {string} name - 音效名称
   * @param {number} duration - 淡入时长(ms)
   */
  fadeIn(name, duration = 1000) {
    const steps = 20
    const stepDuration = duration / steps
    const volumeStep = audioManager.volume / steps
    
    audioManager.play(name, { volume: 0 })
    
    for (let i = 1; i <= steps; i++) {
      setTimeout(() => {
        const audio = audioManager.audioPool.get(name)
        if (audio) {
          audio.volume = volumeStep * i
        }
      }, stepDuration * i)
    }
  },
  
  /**
   * 创建音效淡出效果
   * @param {string} name - 音效名称
   * @param {number} duration - 淡出时长(ms)
   */
  fadeOut(name, duration = 1000) {
    const audio = audioManager.audioPool.get(name)
    if (!audio) return
    
    const steps = 20
    const stepDuration = duration / steps
    const volumeStep = audio.volume / steps
    
    for (let i = 1; i <= steps; i++) {
      setTimeout(() => {
        if (audio) {
          audio.volume = Math.max(0, audio.volume - volumeStep)
          if (i === steps) {
            audioManager.stop(name)
          }
        }
      }, stepDuration * i)
    }
  }
}

<template>
  <view class="idcard-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="header-title">证件号生成器</text>
      <text class="header-desc">仅供测试使用，请勿用于非法用途</text>
    </view>
    
    <!-- 生成配置 -->
    <view class="config-section card">
      <view class="config-header">
        <text class="config-title">生成配置</text>
      </view>
      
      <view class="config-form">
        <!-- 地区选择 -->
        <view class="form-group">
          <text class="form-label">地区</text>
          <picker 
            :value="regionIndex" 
            :range="regions" 
            range-key="name"
            @change="onRegionChange"
          >
            <view class="picker-input">
              {{ regions[regionIndex].name }}
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <!-- 性别选择 -->
        <view class="form-group">
          <text class="form-label">性别</text>
          <view class="radio-group">
            <label class="radio-item" v-for="gender in genders" :key="gender.value">
              <radio 
                :value="gender.value" 
                :checked="selectedGender === gender.value"
                @change="onGenderChange"
              />
              <text class="radio-text">{{ gender.name }}</text>
            </label>
          </view>
        </view>
        
        <!-- 年龄范围 -->
        <view class="form-group">
          <text class="form-label">年龄范围</text>
          <view class="age-range">
            <input 
              class="age-input" 
              type="number" 
              v-model="minAge" 
              placeholder="最小年龄"
            />
            <text class="age-separator">-</text>
            <input 
              class="age-input" 
              type="number" 
              v-model="maxAge" 
              placeholder="最大年龄"
            />
          </view>
        </view>
        
        <!-- 生成数量 -->
        <view class="form-group">
          <text class="form-label">生成数量</text>
          <slider 
            :value="generateCount" 
            :min="1" 
            :max="50" 
            @change="onCountChange"
            activeColor="#10b981"
          />
          <text class="count-text">{{ generateCount }}个</text>
        </view>
      </view>
      
      <!-- 生成按钮 -->
      <button class="generate-btn btn btn-success btn-block" @click="generateIdCards">
        生成证件号
      </button>
    </view>
    
    <!-- 生成结果 -->
    <view class="result-section card" v-if="generatedIds.length > 0">
      <view class="result-header">
        <text class="result-title">生成结果</text>
        <view class="result-actions">
          <button class="btn btn-small btn-outline" @click="copyAll">全部复制</button>
          <button class="btn btn-small btn-outline" @click="clearResults">清空</button>
        </view>
      </view>
      
      <view class="result-list">
        <view 
          class="result-item" 
          v-for="(item, index) in generatedIds" 
          :key="index"
          @click="copyItem(item)"
        >
          <view class="id-info">
            <text class="id-number">{{ item.id }}</text>
            <text class="id-details">{{ item.region }} | {{ item.gender }} | {{ item.age }}岁</text>
          </view>
          <view class="copy-icon">📋</view>
        </view>
      </view>
    </view>
    
    <!-- 使用说明 */
    <view class="notice-section card">
      <view class="notice-header">
        <text class="notice-title">⚠️ 重要说明</text>
      </view>
      <view class="notice-content">
        <text class="notice-text">1. 本工具生成的证件号仅供软件测试使用</text>
        <text class="notice-text">2. 严禁用于任何违法违规活动</text>
        <text class="notice-text">3. 生成的号码符合国标GB 11643-1999规范</text>
        <text class="notice-text">4. 使用本工具即表示同意相关条款</text>
      </view>
    </view>
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'

export default {
  data() {
    return {
      regionIndex: 0,
      selectedGender: 'random',
      minAge: 18,
      maxAge: 60,
      generateCount: 5,
      generatedIds: [],
      
      regions: [
        { name: '北京市', code: '110000' },
        { name: '上海市', code: '310000' },
        { name: '广东省', code: '440000' },
        { name: '浙江省', code: '330000' },
        { name: '江苏省', code: '320000' },
        { name: '山东省', code: '370000' },
        { name: '河南省', code: '410000' },
        { name: '四川省', code: '510000' },
        { name: '湖北省', code: '420000' },
        { name: '湖南省', code: '430000' }
      ],
      
      genders: [
        { name: '随机', value: 'random' },
        { name: '男', value: 'male' },
        { name: '女', value: 'female' }
      ]
    }
  },
  
  computed: {
    redPacketStore() {
      return useRedPacketStore()
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      // 检查是否首次使用工具
      const firstUse = uni.getStorageSync('idcard_first_use')
      if (!firstUse) {
        uni.setStorageSync('idcard_first_use', true)
        this.redPacketStore.checkUserBehavior('use_any_tool')
      }
    },
    
    // 地区选择变化
    onRegionChange(e) {
      this.regionIndex = e.detail.value
    },
    
    // 性别选择变化
    onGenderChange(e) {
      this.selectedGender = e.detail.value
    },
    
    // 数量变化
    onCountChange(e) {
      this.generateCount = e.detail.value
    },
    
    // 生成证件号
    generateIdCards() {
      this.generatedIds = []
      
      for (let i = 0; i < this.generateCount; i++) {
        const idCard = this.generateSingleId()
        this.generatedIds.push(idCard)
      }
      
      // 记录使用统计
      this.recordUsage()
      
      uni.showToast({
        title: `成功生成${this.generateCount}个证件号`,
        icon: 'success'
      })
    },
    
    // 生成单个证件号
    generateSingleId() {
      const region = this.regions[this.regionIndex]
      const age = Math.floor(Math.random() * (this.maxAge - this.minAge + 1)) + this.minAge
      const birthYear = new Date().getFullYear() - age
      const birthMonth = Math.floor(Math.random() * 12) + 1
      const birthDay = Math.floor(Math.random() * 28) + 1
      
      // 地区码（前6位）
      const regionCode = region.code.substring(0, 6)
      
      // 出生日期（8位）
      const birthDate = `${birthYear}${birthMonth.toString().padStart(2, '0')}${birthDay.toString().padStart(2, '0')}`
      
      // 顺序码（3位）
      const sequenceCode = Math.floor(Math.random() * 999).toString().padStart(3, '0')
      
      // 性别码（第17位，奇数为男，偶数为女）
      let genderCode
      if (this.selectedGender === 'male') {
        genderCode = Math.floor(Math.random() * 5) * 2 + 1 // 奇数
      } else if (this.selectedGender === 'female') {
        genderCode = Math.floor(Math.random() * 5) * 2 // 偶数
      } else {
        genderCode = Math.floor(Math.random() * 10) // 随机
      }
      
      // 前17位
      const first17 = regionCode + birthDate + sequenceCode + genderCode
      
      // 计算校验码
      const checkCode = this.calculateCheckCode(first17)
      
      const idNumber = first17 + checkCode
      const gender = genderCode % 2 === 1 ? '男' : '女'
      
      return {
        id: idNumber,
        region: region.name,
        gender: gender,
        age: age,
        birthDate: `${birthYear}-${birthMonth.toString().padStart(2, '0')}-${birthDay.toString().padStart(2, '0')}`
      }
    },
    
    // 计算校验码
    calculateCheckCode(first17) {
      const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
      
      let sum = 0
      for (let i = 0; i < 17; i++) {
        sum += parseInt(first17[i]) * weights[i]
      }
      
      const remainder = sum % 11
      return checkCodes[remainder]
    },
    
    // 复制单个证件号
    copyItem(item) {
      uni.setClipboardData({
        data: item.id,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    
    // 复制全部
    copyAll() {
      const allIds = this.generatedIds.map(item => item.id).join('\n')
      uni.setClipboardData({
        data: allIds,
        success: () => {
          uni.showToast({
            title: '已复制全部证件号',
            icon: 'success'
          })
        }
      })
    },
    
    // 清空结果
    clearResults() {
      this.generatedIds = []
      uni.showToast({
        title: '已清空结果',
        icon: 'success'
      })
    },
    
    // 记录使用统计
    recordUsage() {
      const stats = uni.getStorageSync('work_stats') || {}
      stats.totalUsage = (stats.totalUsage || 0) + 1
      stats.filesProcessed = (stats.filesProcessed || 0) + this.generateCount
      stats.timeSaved = (stats.timeSaved || 0) + Math.floor(this.generateCount / 5) // 假设每5个节省1分钟
      uni.setStorageSync('work_stats', stats)
    }
  }
}
</script>

<style lang="scss" scoped>
.idcard-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.header-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 配置区域 */
.config-header {
  margin-bottom: 32rpx;
}

.config-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

/* 选择器 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
  border: 2rpx solid var(--border-color);
  font-size: 30rpx;
  color: var(--text-color);
}

.picker-arrow {
  color: var(--text-light);
  font-size: 24rpx;
}

/* 单选组 */
.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.radio-text {
  font-size: 28rpx;
  color: var(--text-color);
}

/* 年龄范围 */
.age-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.age-input {
  flex: 1;
  padding: 24rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
  border: 2rpx solid var(--border-color);
  font-size: 30rpx;
  color: var(--text-color);
  text-align: center;
}

.age-separator {
  font-size: 30rpx;
  color: var(--text-light);
}

/* 数量显示 */
.count-text {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  margin-top: 16rpx;
  text-align: center;
  display: block;
}

/* 生成按钮 */
.generate-btn {
  margin-top: 32rpx;
  padding: 32rpx;
  font-size: 36rpx;
}

/* 结果区域 */
.result-section {
  margin-top: 32rpx;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

/* 结果列表 */
.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
  margin-bottom: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  background: #f1f5f9;
}

.result-item:last-child {
  margin-bottom: 0;
}

.id-info {
  flex: 1;
}

.id-number {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
  font-family: 'Courier New', monospace;
}

.id-details {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

.copy-icon {
  font-size: 32rpx;
  color: var(--text-light);
}

/* 说明区域 */
.notice-section {
  margin-top: 32rpx;
}

.notice-header {
  margin-bottom: 24rpx;
}

.notice-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--warning-color);
}

.notice-content {
  line-height: 1.8;
}

.notice-text {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 8rpx;
}
</style>

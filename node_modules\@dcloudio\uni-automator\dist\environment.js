"use strict";function t(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}const e=new(t(require("./index.js")).default);let n,r=!1;try{n=require("jest-environment-node")}catch(t){n=require(require.resolve("jest-environment-node",{paths:[process.cwd()]}))}n&&n.TestEnvironment&&(r=!0,n=n.TestEnvironment);module.exports=class extends n{constructor(t,e){super(r?{projectConfig:t}:t),process.env.UNI_AUTOMATOR_CONFIG?this.launchOptions=require(process.env.UNI_AUTOMATOR_CONFIG):this.launchOptions=t.testEnvironmentOptions}async setup(){await super.setup();const t=global;if(t.__init__){if(!t.program)throw Error("Program init failed")}else t.__init__=!0,this.launchOptions.platform=this.launchOptions.platform||process.env.UNI_PLATFORM,t.program=await e.launch(this.launchOptions),this.launchOptions.devtools&&this.launchOptions.devtools.remote&&await t.program.remote(!0);this.global.program=t.program}async teardown(){await super.teardown()}};

<template>
  <view class="watermark-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="header-title">图片去水印</text>
      <text class="header-desc">AI智能去除图片水印，保持高清画质</text>
    </view>
    
    <!-- 上传区域 -->
    <view class="upload-section card" v-if="!selectedImage">
      <view class="upload-area" @click="selectImage">
        <view class="upload-icon">📷</view>
        <text class="upload-text">点击选择图片</text>
        <text class="upload-desc">支持JPG、PNG格式，最大10MB</text>
      </view>
      
      <!-- 示例图片 -->
      <view class="example-section">
        <text class="example-title">效果示例</text>
        <view class="example-grid">
          <view class="example-item">
            <image class="example-image" src="/static/images/watermark-before.jpg" mode="aspectFit" />
            <text class="example-label">处理前</text>
          </view>
          <view class="example-arrow">→</view>
          <view class="example-item">
            <image class="example-image" src="/static/images/watermark-after.jpg" mode="aspectFit" />
            <text class="example-label">处理后</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 图片预览和处理 -->
    <view class="process-section" v-if="selectedImage">
      <!-- 原图预览 -->
      <view class="image-preview card">
        <view class="preview-header">
          <text class="preview-title">原图预览</text>
          <button class="btn btn-small btn-outline" @click="reselect">重新选择</button>
        </view>
        <view class="image-container">
          <image 
            class="preview-image" 
            :src="selectedImage" 
            mode="aspectFit"
            @load="onImageLoad"
          />
          <view class="image-info">
            <text class="info-item">尺寸: {{ imageInfo.width }}×{{ imageInfo.height }}</text>
            <text class="info-item">大小: {{ formatFileSize(imageInfo.size) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 处理选项 -->
      <view class="options-section card">
        <view class="options-header">
          <text class="options-title">处理选项</text>
        </view>
        
        <view class="option-group">
          <text class="option-label">去水印强度</text>
          <view class="strength-options">
            <label 
              class="strength-item" 
              v-for="strength in strengthOptions" 
              :key="strength.value"
            >
              <radio 
                :value="strength.value" 
                :checked="selectedStrength === strength.value"
                @change="onStrengthChange"
              />
              <text class="strength-text">{{ strength.name }}</text>
            </label>
          </view>
        </view>
        
        <view class="option-group">
          <text class="option-label">输出质量</text>
          <slider 
            :value="outputQuality" 
            :min="50" 
            :max="100" 
            @change="onQualityChange"
            activeColor="#10b981"
          />
          <text class="quality-text">{{ outputQuality }}%</text>
        </view>
        
        <view class="option-group">
          <view class="checkbox-group">
            <label class="checkbox-item">
              <checkbox 
                :checked="preserveOriginal" 
                @change="onPreserveChange"
              />
              <text class="checkbox-text">保留原图</text>
            </label>
            <label class="checkbox-item">
              <checkbox 
                :checked="autoEnhance" 
                @change="onEnhanceChange"
              />
              <text class="checkbox-text">自动增强</text>
            </label>
          </view>
        </view>
      </view>
      
      <!-- 处理按钮 -->
      <view class="action-section">
        <button 
          class="process-btn btn btn-success btn-block" 
          @click="processImage"
          :disabled="isProcessing"
        >
          <text v-if="!isProcessing">开始处理</text>
          <view v-else class="processing-content">
            <view class="loading-spinner"></view>
            <text class="processing-text">AI处理中... {{ processProgress }}%</text>
          </view>
        </button>
      </view>
    </view>
    
    <!-- 处理结果 -->
    <view class="result-section card" v-if="processedImage">
      <view class="result-header">
        <text class="result-title">处理结果</text>
        <view class="result-actions">
          <button class="btn btn-small btn-primary" @click="downloadImage">下载</button>
          <button class="btn btn-small btn-outline" @click="shareImage">分享</button>
        </view>
      </view>
      
      <!-- 对比显示 -->
      <view class="comparison-view">
        <view class="comparison-item">
          <image class="comparison-image" :src="selectedImage" mode="aspectFit" />
          <text class="comparison-label">处理前</text>
        </view>
        <view class="comparison-item">
          <image class="comparison-image" :src="processedImage" mode="aspectFit" />
          <text class="comparison-label">处理后</text>
        </view>
      </view>
      
      <!-- 处理统计 -->
      <view class="process-stats">
        <view class="stat-item">
          <text class="stat-label">处理时间</text>
          <text class="stat-value">{{ processTime }}秒</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">文件大小</text>
          <text class="stat-value">{{ formatFileSize(resultSize) }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">压缩率</text>
          <text class="stat-value">{{ compressionRate }}%</text>
        </view>
      </view>
    </view>
    
    <!-- 使用说明 -->
    <view class="help-section card">
      <view class="help-header">
        <text class="help-title">💡 使用说明</text>
      </view>
      <view class="help-content">
        <text class="help-text">1. 选择需要去水印的图片文件</text>
        <text class="help-text">2. 调整去水印强度和输出质量</text>
        <text class="help-text">3. 点击开始处理，等待AI处理完成</text>
        <text class="help-text">4. 下载或分享处理后的图片</text>
        <text class="help-text">5. 本工具仅供学习交流使用</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedImage: '',
      processedImage: '',
      isProcessing: false,
      processProgress: 0,
      
      // 图片信息
      imageInfo: {
        width: 0,
        height: 0,
        size: 0
      },
      
      // 处理选项
      selectedStrength: 'medium',
      outputQuality: 85,
      preserveOriginal: true,
      autoEnhance: false,
      
      // 处理结果
      processTime: 0,
      resultSize: 0,
      
      // 配置选项
      strengthOptions: [
        { name: '轻度', value: 'light' },
        { name: '中度', value: 'medium' },
        { name: '强力', value: 'strong' }
      ]
    }
  },
  
  computed: {
    compressionRate() {
      if (this.imageInfo.size === 0 || this.resultSize === 0) return 0
      return Math.round((1 - this.resultSize / this.imageInfo.size) * 100)
    }
  },
  
  methods: {
    // 选择图片
    selectImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          this.selectedImage = tempFilePath
          
          // 获取图片信息
          uni.getImageInfo({
            src: tempFilePath,
            success: (info) => {
              this.imageInfo = {
                width: info.width,
                height: info.height,
                size: info.size || 0
              }
            }
          })
        },
        fail: (err) => {
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 重新选择
    reselect() {
      this.selectedImage = ''
      this.processedImage = ''
      this.imageInfo = { width: 0, height: 0, size: 0 }
    },
    
    // 图片加载完成
    onImageLoad(e) {
      // 图片加载完成的处理
    },
    
    // 强度选择变化
    onStrengthChange(e) {
      this.selectedStrength = e.detail.value
    },
    
    // 质量变化
    onQualityChange(e) {
      this.outputQuality = e.detail.value
    },
    
    // 保留原图变化
    onPreserveChange(e) {
      this.preserveOriginal = e.detail.value
    },
    
    // 自动增强变化
    onEnhanceChange(e) {
      this.autoEnhance = e.detail.value
    },
    
    // 处理图片
    async processImage() {
      if (!this.selectedImage) {
        uni.showToast({
          title: '请先选择图片',
          icon: 'none'
        })
        return
      }
      
      this.isProcessing = true
      this.processProgress = 0
      
      const startTime = Date.now()
      
      // 模拟处理进度
      const progressInterval = setInterval(() => {
        this.processProgress += Math.random() * 15
        if (this.processProgress >= 95) {
          this.processProgress = 95
        }
      }, 200)
      
      try {
        // 模拟AI处理（实际应用中这里会调用真实的API）
        await this.simulateProcessing()
        
        clearInterval(progressInterval)
        this.processProgress = 100
        
        // 模拟处理结果
        this.processedImage = this.selectedImage // 实际应用中这里是处理后的图片
        this.processTime = Math.round((Date.now() - startTime) / 1000)
        this.resultSize = Math.round(this.imageInfo.size * 0.8) // 模拟压缩后大小
        
        this.isProcessing = false
        
        // 记录使用统计
        this.recordUsage()
        
        uni.showToast({
          title: '处理完成！',
          icon: 'success'
        })
        
        // 可能触发红包奖励
        if (Math.random() < 0.2) {
          setTimeout(() => {
            this.showToolRedPacket()
          }, 1500)
        }
        
      } catch (error) {
        clearInterval(progressInterval)
        this.isProcessing = false
        
        uni.showToast({
          title: '处理失败，请重试',
          icon: 'none'
        })
      }
    },
    
    // 模拟处理过程
    simulateProcessing() {
      return new Promise((resolve) => {
        setTimeout(resolve, 3000 + Math.random() * 2000)
      })
    },
    
    // 下载图片
    downloadImage() {
      if (!this.processedImage) return
      
      uni.saveImageToPhotosAlbum({
        filePath: this.processedImage,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: () => {
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 分享图片
    shareImage() {
      if (!this.processedImage) return
      
      // #ifdef MP-WEIXIN
      uni.shareAppMessage({
        title: '我用AI去水印工具处理了一张图片',
        path: '/pages/work/watermark/image',
        imageUrl: this.processedImage
      })
      // #endif
      
      // #ifndef MP-WEIXIN
      uni.showToast({
        title: '分享功能暂不支持',
        icon: 'none'
      })
      // #endif
    },
    
    // 显示工具使用红包
    showToolRedPacket() {
      uni.$emit('show_redpacket', {
        type: 'task',
        amount: Math.floor(Math.random() * 5) + 2,
        title: '工具使用红包',
        message: '感谢使用图片去水印工具！',
        animation: 'shake'
      })
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },
    
    // 记录使用统计
    recordUsage() {
      const stats = uni.getStorageSync('work_stats') || {}
      stats.totalUsage = (stats.totalUsage || 0) + 1
      stats.filesProcessed = (stats.filesProcessed || 0) + 1
      stats.timeSaved = (stats.timeSaved || 0) + 5 // 假设节省5分钟
      uni.setStorageSync('work_stats', stats)
    }
  }
}
</script>

<style lang="scss" scoped>
.watermark-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.header-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 上传区域 */
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  border: 4rpx dashed var(--border-color);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 32rpx;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.05);
}

.upload-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.upload-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 示例区域 */
.example-section {
  text-align: center;
}

.example-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 24rpx;
}

.example-grid {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
}

.example-item {
  text-align: center;
}

.example-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: var(--radius);
  margin-bottom: 16rpx;
}

.example-label {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

.example-arrow {
  font-size: 48rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 图片预览 */
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.image-container {
  text-align: center;
}

.preview-image {
  width: 100%;
  max-height: 400rpx;
  border-radius: var(--radius);
  margin-bottom: 16rpx;
}

.image-info {
  display: flex;
  justify-content: center;
  gap: 32rpx;
}

.info-item {
  font-size: 26rpx;
  color: var(--text-light);
}

/* 处理选项 */
.options-header {
  margin-bottom: 32rpx;
}

.options-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.option-group {
  margin-bottom: 32rpx;
}

.option-label {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

/* 强度选项 */
.strength-options {
  display: flex;
  gap: 32rpx;
}

.strength-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.strength-text {
  font-size: 28rpx;
  color: var(--text-color);
}

/* 质量滑块 */
.quality-text {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  margin-top: 16rpx;
  text-align: center;
  display: block;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  gap: 32rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.checkbox-text {
  font-size: 28rpx;
  color: var(--text-color);
}

/* 处理按钮 */
.action-section {
  margin: 32rpx 0;
}

.process-btn {
  padding: 32rpx;
  font-size: 36rpx;
}

.processing-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.processing-text {
  font-size: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 处理结果 */
.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

/* 对比显示 */
.comparison-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.comparison-item {
  text-align: center;
}

.comparison-image {
  width: 100%;
  height: 300rpx;
  border-radius: var(--radius);
  margin-bottom: 16rpx;
}

.comparison-label {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 处理统计 */
.process-stats {
  display: flex;
  justify-content: space-around;
  padding: 24rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--primary-color);
  display: block;
}

/* 帮助说明 */
.help-header {
  margin-bottom: 24rpx;
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.help-content {
  line-height: 1.8;
}

.help-text {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 8rpx;
}

@media (max-width: 500rpx) {
  .example-grid {
    flex-direction: column;
    gap: 24rpx;
  }
  
  .example-arrow {
    transform: rotate(90deg);
  }
  
  .comparison-view {
    grid-template-columns: 1fr;
  }
  
  .strength-options,
  .checkbox-group {
    flex-direction: column;
    gap: 16rpx;
  }
}
</style>

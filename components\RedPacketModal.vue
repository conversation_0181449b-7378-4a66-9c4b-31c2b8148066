<template>
  <view class="redpacket-modal" v-if="visible" @click="handleMaskClick">
    <!-- 背景遮罩 -->
    <view class="modal-mask" :class="{ 'fade-in': showAnimation }"></view>
    
    <!-- 红包容器 -->
    <view class="redpacket-container" :class="animationClass" @click.stop>
      <!-- 红包封面 -->
      <view class="redpacket-cover" v-if="!isOpened">
        <view class="redpacket-bg" :style="{ background: redpacketGradient }">
          <!-- 装饰图案 -->
          <view class="decoration-pattern">
            <view class="pattern-item" v-for="i in 8" :key="i" :style="getPatternStyle(i)">
              ✨
            </view>
          </view>
          
          <!-- 红包主体 -->
          <view class="redpacket-main">
            <view class="redpacket-title">{{ packetData.title || '恭喜发财' }}</view>
            <view class="redpacket-message">{{ packetData.message || '大吉大利' }}</view>
            
            <!-- 红包图标 -->
            <view class="redpacket-icon-container">
              <view class="redpacket-icon" :class="{ 'pulse': pulseAnimation }">
                🧧
              </view>
              <view class="glow-effect"></view>
            </view>
            
            <!-- 开启按钮 -->
            <button class="open-btn" @click="openRedPacket" :disabled="isOpening">
              <text v-if="!isOpening">{{ openButtonText }}</text>
              <view v-else class="loading-spinner"></view>
            </button>
          </view>
          
          <!-- 底部装饰 -->
          <view class="bottom-decoration">
            <view class="wave-line"></view>
          </view>
        </view>
      </view>
      
      <!-- 红包内容（已开启状态） -->
      <view class="redpacket-content" v-else>
        <view class="content-bg">
          <!-- 成功图标 -->
          <view class="success-icon" :class="{ 'bounce': showSuccess }">
            🎉
          </view>
          
          <!-- 金额显示 -->
          <view class="amount-display">
            <text class="amount-number">{{ formatAmount(packetData.amount) }}</text>
            <text class="amount-unit">{{ getAmountUnit(packetData.amount) }}</text>
          </view>
          
          <!-- 祝福语 */
          <view class="blessing-text">
            {{ blessingMessage }}
          </view>
          
          <!-- 红包详情 */
          <view class="packet-details">
            <view class="detail-item">
              <text class="detail-label">红包类型</text>
              <text class="detail-value">{{ getPacketTypeName(packetData.type) }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">获得时间</text>
              <text class="detail-value">{{ formatTime(Date.now()) }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button class="btn btn-primary share-btn" @click="shareRedPacket">
              分享好友
            </button>
            <button class="btn btn-outline close-btn" @click="closeModal">
              我知道了
            </button>
          </view>
        </view>
        
        <!-- 金币掉落动画 -->
        <view class="coins-container" v-if="showCoins">
          <view 
            class="coin" 
            v-for="coin in coins" 
            :key="coin.id"
            :style="getCoinStyle(coin)"
            :class="{ 'coin-drop': coin.animate }"
          >
            💰
          </view>
        </view>
      </view>
    </view>
    
    <!-- 烟花效果 -->
    <view class="fireworks-container" v-if="showFireworks">
      <view 
        class="firework" 
        v-for="firework in fireworks" 
        :key="firework.id"
        :style="getFireworkStyle(firework)"
      >
        ✨
      </view>
    </view>
  </view>
</template>

<script>
import { RedPacketUtils } from '@/utils/redpacket'

export default {
  name: 'RedPacketModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    packetData: {
      type: Object,
      default: () => ({
        type: 'special',
        amount: 10,
        title: '恭喜发财',
        message: '大吉大利',
        animation: 'bounce'
      })
    }
  },
  
  data() {
    return {
      isOpened: false,
      isOpening: false,
      showAnimation: false,
      showSuccess: false,
      showCoins: false,
      showFireworks: false,
      pulseAnimation: true,
      
      coins: [],
      fireworks: [],
      
      blessingMessage: '',
      
      animationTimer: null,
      coinTimer: null,
      fireworkTimer: null
    }
  },
  
  computed: {
    // 动画类名
    animationClass() {
      if (!this.showAnimation) return ''
      return `animate-${this.packetData.animation || 'bounce'}`
    },
    
    // 红包渐变色
    redpacketGradient() {
      return RedPacketUtils.getRedPacketGradient(this.packetData.amount)
    },
    
    // 开启按钮文字
    openButtonText() {
      const texts = ['点击开启', '拆红包', '开启惊喜', '领取奖励']
      return texts[Math.floor(Math.random() * texts.length)]
    }
  },
  
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initModal()
      } else {
        this.resetModal()
      }
    }
  },
  
  methods: {
    // 初始化弹窗
    initModal() {
      this.showAnimation = true
      this.blessingMessage = RedPacketUtils.getRedPacketMessage(this.packetData.type)
      
      // 播放出现音效
      RedPacketUtils.playRedPacketSound('pop')
      
      // 启动脉冲动画
      this.startPulseAnimation()
    },
    
    // 重置弹窗状态
    resetModal() {
      this.isOpened = false
      this.isOpening = false
      this.showAnimation = false
      this.showSuccess = false
      this.showCoins = false
      this.showFireworks = false
      this.pulseAnimation = true
      
      this.clearTimers()
    },
    
    // 清除定时器
    clearTimers() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
      if (this.coinTimer) {
        clearTimeout(this.coinTimer)
        this.coinTimer = null
      }
      if (this.fireworkTimer) {
        clearTimeout(this.fireworkTimer)
        this.fireworkTimer = null
      }
    },
    
    // 启动脉冲动画
    startPulseAnimation() {
      setInterval(() => {
        this.pulseAnimation = !this.pulseAnimation
      }, 1500)
    },
    
    // 开启红包
    async openRedPacket() {
      if (this.isOpening) return
      
      this.isOpening = true
      this.pulseAnimation = false
      
      // 播放开启音效
      RedPacketUtils.playRedPacketSound('open')
      
      // 延迟显示内容
      this.animationTimer = setTimeout(() => {
        this.isOpened = true
        this.isOpening = false
        this.showSuccess = true
        
        // 启动金币掉落动画
        this.startCoinAnimation()
        
        // 启动烟花效果
        this.startFireworkAnimation()
        
        // 播放成功音效
        RedPacketUtils.playRedPacketSound('success')
        
        // 触发领取事件
        this.$emit('claim', this.packetData)
      }, 800)
    },
    
    // 启动金币掉落动画
    startCoinAnimation() {
      this.coins = RedPacketUtils.createCoinDropAnimation(12)
      this.showCoins = true
      
      // 逐个启动金币动画
      this.coins.forEach((coin, index) => {
        setTimeout(() => {
          coin.animate = true
        }, index * 100)
      })
      
      // 清理金币
      this.coinTimer = setTimeout(() => {
        this.showCoins = false
        this.coins = []
      }, 3000)
    },
    
    // 启动烟花效果
    startFireworkAnimation() {
      this.fireworks = []
      for (let i = 0; i < 6; i++) {
        this.fireworks.push({
          id: i,
          x: Math.random() * 300 + 50,
          y: Math.random() * 200 + 100,
          delay: Math.random() * 1000
        })
      }
      
      this.showFireworks = true
      
      this.fireworkTimer = setTimeout(() => {
        this.showFireworks = false
        this.fireworks = []
      }, 2000)
    },
    
    // 获取装饰图案样式
    getPatternStyle(index) {
      const angle = (index - 1) * 45
      const radius = 120
      const x = Math.cos(angle * Math.PI / 180) * radius
      const y = Math.sin(angle * Math.PI / 180) * radius
      
      return {
        transform: `translate(${x}rpx, ${y}rpx) rotate(${angle}deg)`,
        animationDelay: `${index * 0.1}s`
      }
    },
    
    // 获取金币样式
    getCoinStyle(coin) {
      return {
        left: `${coin.x}rpx`,
        top: `${coin.y}rpx`,
        transform: `rotate(${coin.rotation}deg)`,
        animationDelay: `${coin.delay}ms`,
        animationDuration: `${coin.duration}ms`
      }
    },
    
    // 获取烟花样式
    getFireworkStyle(firework) {
      return {
        left: `${firework.x}rpx`,
        top: `${firework.y}rpx`,
        animationDelay: `${firework.delay}ms`
      }
    },
    
    // 格式化金额
    formatAmount(amount) {
      return RedPacketUtils.formatAmount(amount).replace(/[元分]/g, '')
    },
    
    // 获取金额单位
    getAmountUnit(amount) {
      return amount >= 100 ? '元' : '分'
    },
    
    // 获取红包类型名称
    getPacketTypeName(type) {
      const typeNames = {
        newuser: '新用户红包',
        signin: '签到红包',
        task: '任务红包',
        special: '特殊红包'
      }
      return typeNames[type] || '红包'
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },
    
    // 分享红包
    shareRedPacket() {
      const shareData = RedPacketUtils.generateShareContent(this.packetData)
      
      // #ifdef MP-WEIXIN
      uni.shareAppMessage(shareData)
      // #endif
      
      // #ifndef MP-WEIXIN
      uni.showToast({
        title: '分享功能暂不支持',
        icon: 'none'
      })
      // #endif
    },
    
    // 关闭弹窗
    closeModal() {
      this.$emit('close')
    },
    
    // 处理遮罩点击
    handleMaskClick() {
      if (this.isOpened) {
        this.closeModal()
      }
    }
  },
  
  beforeUnmount() {
    this.clearTimers()
  }
}
</script>

<style lang="scss" scoped>
.redpacket-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景遮罩 */
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-mask.fade-in {
  opacity: 1;
}

/* 红包容器 */
.redpacket-container {
  position: relative;
  width: 600rpx;
  max-width: 90vw;
  z-index: 10000;
}

/* 动画类 */
.animate-bounce {
  animation: redpacketBounce 0.6s ease-in-out;
}

.animate-shake {
  animation: redpacketShake 0.5s ease-in-out;
}

.animate-pulse {
  animation: redpacketPulse 0.8s ease-in-out;
}

.animate-rainbow {
  animation: redpacketRainbow 1s linear infinite;
}

@keyframes redpacketBounce {
  0% { transform: scale(0.3) translateY(100rpx); opacity: 0; }
  50% { transform: scale(1.05) translateY(-20rpx); opacity: 1; }
  70% { transform: scale(0.95) translateY(10rpx); }
  100% { transform: scale(1) translateY(0); }
}

@keyframes redpacketShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

@keyframes redpacketPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes redpacketRainbow {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

/* 红包封面 */
.redpacket-cover {
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.redpacket-bg {
  position: relative;
  padding: 60rpx 40rpx;
  color: white;
  text-align: center;
  overflow: hidden;
}

/* 装饰图案 */
.decoration-pattern {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.pattern-item {
  position: absolute;
  font-size: 24rpx;
  opacity: 0.3;
  animation: patternFloat 3s ease-in-out infinite;
}

@keyframes patternFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

/* 红包主体 */
.redpacket-main {
  position: relative;
  z-index: 2;
}

.redpacket-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.redpacket-message {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 60rpx;
}

/* 红包图标 */
.redpacket-icon-container {
  position: relative;
  margin: 60rpx 0;
}

.redpacket-icon {
  font-size: 120rpx;
  display: inline-block;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.redpacket-icon.pulse {
  animation: iconPulse 1.5s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glowPulse 2s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
}

/* 开启按钮 */
.open-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 24rpx 60rpx;
  border-radius: 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.open-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-4rpx);
}

.open-btn:disabled {
  opacity: 0.7;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  overflow: hidden;
}

.wave-line {
  width: 200%;
  height: 40rpx;
  background: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 20rpx,
    rgba(255, 255, 255, 0.1) 20rpx,
    rgba(255, 255, 255, 0.1) 40rpx
  );
  animation: waveMove 3s linear infinite;
}

@keyframes waveMove {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

/* 红包内容 */
.redpacket-content {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.content-bg {
  padding: 60rpx 40rpx;
  text-align: center;
}

/* 成功图标 */
.success-icon {
  font-size: 100rpx;
  margin-bottom: 40rpx;
  display: inline-block;
}

.success-icon.bounce {
  animation: successBounce 0.6s ease-in-out;
}

@keyframes successBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 金额显示 */
.amount-display {
  margin-bottom: 40rpx;
}

.amount-number {
  font-size: 80rpx;
  font-weight: bold;
  color: var(--danger-color);
  display: inline-block;
}

.amount-unit {
  font-size: 40rpx;
  color: var(--danger-color);
  margin-left: 8rpx;
}

/* 祝福语 */
.blessing-text {
  font-size: 32rpx;
  color: var(--text-color);
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 红包详情 */
.packet-details {
  background: var(--bg-color);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: var(--text-light);
}

.detail-value {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.share-btn,
.close-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
}

/* 金币掉落动画 */
.coins-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.coin {
  position: absolute;
  font-size: 40rpx;
  opacity: 0;
}

.coin.coin-drop {
  animation: coinDrop 2s ease-in forwards;
}

@keyframes coinDrop {
  0% {
    opacity: 1;
    transform: translateY(-100rpx) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(800rpx) rotate(720deg);
  }
}

/* 烟花效果 */
.fireworks-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.firework {
  position: absolute;
  font-size: 32rpx;
  animation: fireworkExplode 1.5s ease-out forwards;
}

@keyframes fireworkExplode {
  0% {
    opacity: 1;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}
</style>

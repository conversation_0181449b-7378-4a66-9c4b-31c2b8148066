<template>
  <view class="index-page">
    <!-- 顶部横幅 -->
    <view class="header-banner gradient-bg">
      <view class="banner-content">
        <text class="banner-title">多功能工具箱</text>
        <text class="banner-subtitle">生活·娱乐·工作 一站式解决方案</text>
      </view>
      
      <!-- 红包入口 -->
      <view class="redpacket-entry" @click="goToRedPacket">
        <view class="redpacket-icon">
          <text class="icon">🧧</text>
          <view class="redpacket-badge" v-if="unclaimedCount > 0">
            {{ unclaimedCount }}
          </view>
        </view>
        <text class="redpacket-text">红包</text>
      </view>
    </view>
    
    <!-- 快速签到卡片 -->
    <view class="signin-card card" v-if="!isTodaySignedIn">
      <view class="signin-content">
        <view class="signin-info">
          <text class="signin-title">每日签到</text>
          <text class="signin-desc">连续签到{{ consecutiveDays }}天，今日可获得奖励</text>
        </view>
        <button class="btn btn-danger signin-btn" @click="handleSignIn">
          立即签到
        </button>
      </view>
    </view>
    
    <!-- 功能模块网格 -->
    <view class="modules-grid">
      <!-- 娱乐模块 -->
      <view class="module-card entertainment" @click="goToModule('entertainment')">
        <view class="module-icon gradient-entertainment">
          <text class="icon">🎮</text>
        </view>
        <view class="module-info">
          <text class="module-title">娱乐游戏</text>
          <text class="module-desc">象棋·五子棋·围棋·消消乐</text>
        </view>
        <view class="module-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 工作模块 -->
      <view class="module-card work" @click="goToModule('work')">
        <view class="module-icon gradient-work">
          <text class="icon">💼</text>
        </view>
        <view class="module-info">
          <text class="module-title">工作工具</text>
          <text class="module-desc">证件号·去水印·实用工具</text>
        </view>
        <view class="module-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 生活模块 -->
      <view class="module-card life" @click="goToModule('life')">
        <view class="module-icon gradient-life">
          <text class="icon">🏠</text>
        </view>
        <view class="module-info">
          <text class="module-title">生活助手</text>
          <text class="module-desc">计算器·健康·出行助手</text>
        </view>
        <view class="module-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
    </view>
    
    <!-- 热门工具推荐 -->
    <view class="hot-tools">
      <view class="section-header">
        <text class="section-title">热门工具</text>
        <text class="section-more">更多 →</text>
      </view>
      
      <view class="tools-grid grid-2">
        <view class="tool-card" @click="goToTool('idcard')">
          <view class="tool-icon" style="background: var(--success-color);">
            <text class="icon">🆔</text>
          </view>
          <text class="tool-title">证件号生成</text>
          <text class="tool-desc">测试用证件号生成器</text>
        </view>
        
        <view class="tool-card" @click="goToTool('chess')">
          <view class="tool-icon" style="background: var(--secondary-color);">
            <text class="icon">♟️</text>
          </view>
          <text class="tool-title">中国象棋</text>
          <text class="tool-desc">经典象棋对弈游戏</text>
        </view>
        
        <view class="tool-card" @click="goToTool('calculator')">
          <view class="tool-icon" style="background: var(--warning-color);">
            <text class="icon">🧮</text>
          </view>
          <text class="tool-title">计算器</text>
          <text class="tool-desc">多功能科学计算器</text>
        </view>
        
        <view class="tool-card" @click="goToTool('watermark')">
          <view class="tool-icon" style="background: var(--primary-color);">
            <text class="icon">🖼️</text>
          </view>
          <text class="tool-title">去水印</text>
          <text class="tool-desc">图片视频去水印</text>
        </view>
      </view>
    </view>
    
    <!-- 使用统计 -->
    <view class="stats-card card">
      <view class="stats-header">
        <text class="stats-title">使用统计</text>
      </view>
      <view class="stats-content">
        <view class="stat-item">
          <text class="stat-number">{{ totalUsage }}</text>
          <text class="stat-label">总使用次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ redPacketStats.count }}</text>
          <text class="stat-label">获得红包</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ redPacketStats.amount }}</text>
          <text class="stat-label">红包金额</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'

export default {
  data() {
    return {
      totalUsage: 0,
      unclaimedCount: 0
    }
  },
  
  computed: {
    redPacketStore() {
      return useRedPacketStore()
    },
    
    isTodaySignedIn() {
      return this.redPacketStore.isTodaySignedIn
    },
    
    consecutiveDays() {
      return this.redPacketStore.getConsecutiveDays
    },
    
    redPacketStats() {
      return this.redPacketStore.getMonthlyStats
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.updateStats()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.redPacketStore.initRedPacketData()
      this.updateStats()
      this.checkUnclaimedRedPackets()
    },
    
    // 更新统计数据
    updateStats() {
      this.totalUsage = uni.getStorageSync('total_usage') || 0
    },
    
    // 检查未领取红包
    checkUnclaimedRedPackets() {
      const availableTasks = this.redPacketStore.getAvailableTaskRedPackets
      this.unclaimedCount = availableTasks.length
    },
    
    // 处理签到
    async handleSignIn() {
      const result = this.redPacketStore.dailySignIn()
      
      if (result.success) {
        uni.showToast({
          title: '签到成功！',
          icon: 'success'
        })
        
        // 如果获得红包，显示红包动画
        if (result.reward.type === 'redpacket') {
          setTimeout(() => {
            this.showSignInRedPacket(result.reward)
          }, 1000)
        }
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    },
    
    // 显示签到红包
    showSignInRedPacket(reward) {
      // 触发全局红包显示事件
      uni.$emit('show_redpacket', {
        type: 'signin',
        amount: reward.amount,
        title: '签到红包',
        message: '恭喜您获得签到奖励！'
      })
    },
    
    // 跳转到模块页面
    goToModule(module) {
      // 记录使用统计
      this.recordUsage()
      
      uni.switchTab({
        url: `/pages/${module}/index`
      })
    },
    
    // 跳转到具体工具
    goToTool(tool) {
      this.recordUsage()
      
      const toolRoutes = {
        idcard: '/pages/work/idcard/index',
        chess: '/pages/entertainment/chess/index',
        calculator: '/pages/life/calculator/index',
        watermark: '/pages/work/watermark/image'
      }
      
      const route = toolRoutes[tool]
      if (route) {
        uni.navigateTo({
          url: route
        })
      }
    },
    
    // 跳转到红包页面
    goToRedPacket() {
      uni.switchTab({
        url: '/pages/redpacket/index'
      })
    },
    
    // 记录使用统计
    recordUsage() {
      this.totalUsage += 1
      uni.setStorageSync('total_usage', this.totalUsage)
      
      // 检查是否触发使用工具红包
      if (this.totalUsage === 1) {
        this.redPacketStore.checkUserBehavior('use_any_tool')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.index-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding-bottom: 120rpx;
}

/* 顶部横幅 */
.header-banner {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.banner-content {
  text-align: center;
}

.banner-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.redpacket-entry {
  position: absolute;
  top: 60rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.redpacket-icon {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.redpacket-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.redpacket-text {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 签到卡片 */
.signin-card {
  margin: 32rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
}

.signin-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.signin-info {
  flex: 1;
}

.signin-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.signin-desc {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.signin-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
}

/* 模块网格 */
.modules-grid {
  padding: 0 32rpx;
  margin-bottom: 48rpx;
}

.module-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.module-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.module-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
  margin-right: 24rpx;
}

.module-info {
  flex: 1;
}

.module-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.module-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

.module-arrow {
  font-size: 32rpx;
  color: var(--text-light);
}

/* 热门工具 */
.hot-tools {
  padding: 0 32rpx;
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.section-more {
  font-size: 28rpx;
  color: var(--primary-color);
  cursor: pointer;
}

/* 统计卡片 */
.stats-card {
  margin: 0 32rpx;
}

.stats-header {
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.stats-content {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}
</style>

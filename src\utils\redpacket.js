/**
 * 红包工具类
 * 提供红包相关的工具函数和动画效果
 */

export class RedPacketUtils {
  
  /**
   * 生成红包动画配置
   * @param {string} type - 动画类型
   * @returns {object} 动画配置
   */
  static getAnimationConfig(type) {
    const animations = {
      bounce: {
        duration: 600,
        timingFunction: 'ease-in-out',
        keyframes: [
          { transform: 'scale(0.3)', opacity: 0 },
          { transform: 'scale(1.05)', opacity: 1 },
          { transform: 'scale(0.95)', opacity: 1 },
          { transform: 'scale(1)', opacity: 1 }
        ]
      },
      shake: {
        duration: 500,
        timingFunction: 'ease-in-out',
        keyframes: [
          { transform: 'translateX(0)' },
          { transform: 'translateX(-10px)' },
          { transform: 'translateX(10px)' },
          { transform: 'translateX(-10px)' },
          { transform: 'translateX(10px)' },
          { transform: 'translateX(0)' }
        ]
      },
      pulse: {
        duration: 800,
        timingFunction: 'ease-in-out',
        keyframes: [
          { transform: 'scale(1)', opacity: 1 },
          { transform: 'scale(1.1)', opacity: 0.8 },
          { transform: 'scale(1)', opacity: 1 }
        ]
      },
      rainbow: {
        duration: 1000,
        timingFunction: 'linear',
        keyframes: [
          { filter: 'hue-rotate(0deg)' },
          { filter: 'hue-rotate(360deg)' }
        ]
      },
      fadeIn: {
        duration: 400,
        timingFunction: 'ease-out',
        keyframes: [
          { opacity: 0, transform: 'translateY(20px)' },
          { opacity: 1, transform: 'translateY(0)' }
        ]
      },
      slideUp: {
        duration: 300,
        timingFunction: 'ease-out',
        keyframes: [
          { transform: 'translateY(100%)' },
          { transform: 'translateY(0)' }
        ]
      }
    }
    
    return animations[type] || animations.bounce
  }
  
  /**
   * 创建金币掉落动画
   * @param {number} count - 金币数量
   * @param {object} container - 容器元素
   */
  static createCoinDropAnimation(count = 10, container) {
    const coins = []
    
    for (let i = 0; i < count; i++) {
      const coin = {
        id: `coin_${i}`,
        x: Math.random() * 300 + 50, // 随机X位置
        y: -50, // 起始Y位置
        rotation: Math.random() * 360, // 随机旋转角度
        delay: Math.random() * 500, // 随机延迟
        duration: 1000 + Math.random() * 500 // 随机持续时间
      }
      coins.push(coin)
    }
    
    return coins
  }
  
  /**
   * 播放红包音效
   * @param {string} type - 音效类型
   */
  static playRedPacketSound(type) {
    const soundMap = {
      open: '/static/audio/redpacket_open.mp3',
      claim: '/static/audio/coin_drop.mp3',
      success: '/static/audio/success.mp3',
      pop: '/static/audio/pop.mp3'
    }
    
    const audioPath = soundMap[type]
    if (!audioPath) return
    
    try {
      const audio = uni.createInnerAudioContext()
      audio.src = audioPath
      audio.volume = 0.8
      audio.play()
      
      audio.onEnded(() => {
        audio.destroy()
      })
      
      audio.onError((err) => {
        console.warn('音效播放失败:', err)
        audio.destroy()
      })
    } catch (error) {
      console.warn('音效播放异常:', error)
    }
  }
  
  /**
   * 格式化红包金额显示
   * @param {number} amount - 金额
   * @returns {string} 格式化后的金额
   */
  static formatAmount(amount) {
    if (amount >= 100) {
      return `${(amount / 100).toFixed(1)}元`
    } else if (amount >= 10) {
      return `${amount}分`
    } else {
      return `${amount}分`
    }
  }
  
  /**
   * 生成红包祝福语
   * @param {string} type - 红包类型
   * @returns {string} 祝福语
   */
  static getRedPacketMessage(type) {
    const messages = {
      newuser: [
        '欢迎加入我们的大家庭！',
        '新用户专享，快来体验吧！',
        '感谢您的信任与支持！',
        '开启美好的工具之旅！'
      ],
      signin: [
        '签到成功，坚持就是胜利！',
        '每日签到，好运连连！',
        '持之以恒，必有收获！',
        '今日签到完成，明天继续加油！'
      ],
      task: [
        '任务完成，奖励到手！',
        '恭喜您完成任务！',
        '努力的人运气不会太差！',
        '继续加油，更多惊喜等着您！'
      ],
      special: [
        '恭喜您获得特殊奖励！',
        '幸运女神眷顾着您！',
        '意外惊喜，好运爆棚！',
        '特殊时刻，特殊奖励！'
      ]
    }
    
    const typeMessages = messages[type] || messages.special
    return typeMessages[Math.floor(Math.random() * typeMessages.length)]
  }
  
  /**
   * 计算红包颜色渐变
   * @param {number} amount - 红包金额
   * @returns {string} CSS渐变色
   */
  static getRedPacketGradient(amount) {
    if (amount >= 50) {
      // 大额红包 - 金色渐变
      return 'linear-gradient(135deg, #ffd700, #ffb347)'
    } else if (amount >= 20) {
      // 中额红包 - 红色渐变
      return 'linear-gradient(135deg, #ff6b6b, #ee5a52)'
    } else if (amount >= 10) {
      // 小额红包 - 橙色渐变
      return 'linear-gradient(135deg, #ff8a65, #ff7043)'
    } else {
      // 微额红包 - 粉色渐变
      return 'linear-gradient(135deg, #f48fb1, #e91e63)'
    }
  }
  
  /**
   * 生成红包封面图案
   * @param {string} type - 红包类型
   * @returns {string} 图案类名或图片路径
   */
  static getRedPacketPattern(type) {
    const patterns = {
      newuser: 'pattern-welcome',
      signin: 'pattern-calendar',
      task: 'pattern-trophy',
      special: 'pattern-star'
    }
    
    return patterns[type] || 'pattern-default'
  }
  
  /**
   * 检查红包领取条件
   * @param {object} redPacket - 红包数据
   * @param {object} userState - 用户状态
   * @returns {object} 检查结果
   */
  static checkClaimCondition(redPacket, userState) {
    const result = {
      canClaim: true,
      reason: ''
    }
    
    // 检查时间限制
    if (redPacket.expireTime && new Date() > new Date(redPacket.expireTime)) {
      result.canClaim = false
      result.reason = '红包已过期'
      return result
    }
    
    // 检查领取次数限制
    if (redPacket.maxClaims && redPacket.claimedCount >= redPacket.maxClaims) {
      result.canClaim = false
      result.reason = '红包已被领完'
      return result
    }
    
    // 检查用户等级限制
    if (redPacket.minLevel && userState.level < redPacket.minLevel) {
      result.canClaim = false
      result.reason = `需要达到${redPacket.minLevel}级才能领取`
      return result
    }
    
    return result
  }
  
  /**
   * 生成红包分享文案
   * @param {object} redPacket - 红包数据
   * @returns {object} 分享数据
   */
  static generateShareContent(redPacket) {
    return {
      title: `我在多功能工具箱获得了${this.formatAmount(redPacket.amount)}红包！`,
      desc: '快来一起使用这个超实用的工具箱吧！',
      path: '/pages/index/index',
      imageUrl: '/static/images/share-redpacket.png'
    }
  }
  
  /**
   * 红包统计分析
   * @param {array} redPackets - 红包列表
   * @returns {object} 统计数据
   */
  static analyzeRedPackets(redPackets) {
    const stats = {
      total: redPackets.length,
      totalAmount: 0,
      byType: {},
      byMonth: {},
      avgAmount: 0,
      maxAmount: 0,
      minAmount: Infinity
    }
    
    redPackets.forEach(packet => {
      stats.totalAmount += packet.amount
      stats.maxAmount = Math.max(stats.maxAmount, packet.amount)
      stats.minAmount = Math.min(stats.minAmount, packet.amount)
      
      // 按类型统计
      if (!stats.byType[packet.type]) {
        stats.byType[packet.type] = { count: 0, amount: 0 }
      }
      stats.byType[packet.type].count++
      stats.byType[packet.type].amount += packet.amount
      
      // 按月份统计
      const month = new Date(packet.claimTime).getMonth()
      if (!stats.byMonth[month]) {
        stats.byMonth[month] = { count: 0, amount: 0 }
      }
      stats.byMonth[month].count++
      stats.byMonth[month].amount += packet.amount
    })
    
    stats.avgAmount = stats.total > 0 ? stats.totalAmount / stats.total : 0
    stats.minAmount = stats.minAmount === Infinity ? 0 : stats.minAmount
    
    return stats
  }
}

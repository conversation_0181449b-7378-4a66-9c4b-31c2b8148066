import { defineStore } from 'pinia'

export const useRedPacketStore = defineStore('redpacket', {
  state: () => ({
    // 用户红包数据
    userRedPackets: [],
    
    // 红包统计
    totalAmount: 0,
    totalCount: 0,
    
    // 签到数据
    signInData: {
      consecutiveDays: 0,
      lastSignInDate: null,
      totalSignInDays: 0
    },
    
    // 任务红包配置
    taskRedPackets: [
      {
        id: 'first_game',
        name: '首次游戏红包',
        description: '完成第一局游戏即可获得',
        amount: 5,
        condition: 'play_first_game',
        claimed: false
      },
      {
        id: 'use_tool',
        name: '工具使用红包',
        description: '使用任意工具即可获得',
        amount: 3,
        condition: 'use_any_tool',
        claimed: false
      },
      {
        id: 'daily_active',
        name: '每日活跃红包',
        description: '每日使用应用超过5分钟',
        amount: 2,
        condition: 'daily_active_5min',
        claimed: false,
        daily: true
      }
    ],
    
    // 签到红包配置
    signInRewards: [
      { day: 1, amount: 1, type: 'coin' },
      { day: 2, amount: 2, type: 'coin' },
      { day: 3, amount: 5, type: 'redpacket' },
      { day: 4, amount: 3, type: 'coin' },
      { day: 5, amount: 4, type: 'coin' },
      { day: 6, amount: 8, type: 'redpacket' },
      { day: 7, amount: 10, type: 'redpacket', special: true }
    ],
    
    // 红包类型配置
    redPacketTypes: {
      newuser: {
        name: '新用户红包',
        minAmount: 10,
        maxAmount: 60,
        animation: 'bounce',
        color: '#ff6b6b'
      },
      signin: {
        name: '签到红包',
        minAmount: 1,
        maxAmount: 10,
        animation: 'shake',
        color: '#4ecdc4'
      },
      task: {
        name: '任务红包',
        minAmount: 2,
        maxAmount: 8,
        animation: 'pulse',
        color: '#45b7d1'
      },
      special: {
        name: '特殊红包',
        minAmount: 15,
        maxAmount: 100,
        animation: 'rainbow',
        color: '#f9ca24'
      }
    }
  }),
  
  getters: {
    // 获取今日是否已签到
    isTodaySignedIn: (state) => {
      const today = new Date().toDateString()
      return state.signInData.lastSignInDate === today
    },
    
    // 获取连续签到天数
    getConsecutiveDays: (state) => {
      return state.signInData.consecutiveDays
    },
    
    // 获取今日签到奖励
    getTodayReward: (state) => {
      const day = (state.signInData.consecutiveDays % 7) + 1
      return state.signInRewards.find(reward => reward.day === day)
    },
    
    // 获取可领取的任务红包
    getAvailableTaskRedPackets: (state) => {
      return state.taskRedPackets.filter(task => !task.claimed)
    },
    
    // 获取红包历史记录
    getRedPacketHistory: (state) => {
      return state.userRedPackets.sort((a, b) => new Date(b.claimTime) - new Date(a.claimTime))
    },
    
    // 获取本月红包统计
    getMonthlyStats: (state) => {
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      
      const monthlyPackets = state.userRedPackets.filter(packet => {
        const packetDate = new Date(packet.claimTime)
        return packetDate.getMonth() === currentMonth && packetDate.getFullYear() === currentYear
      })
      
      return {
        count: monthlyPackets.length,
        amount: monthlyPackets.reduce((sum, packet) => sum + packet.amount, 0)
      }
    }
  },
  
  actions: {
    // 初始化红包数据
    initRedPacketData() {
      const savedData = uni.getStorageSync('redpacket_data')
      if (savedData) {
        this.userRedPackets = savedData.userRedPackets || []
        this.totalAmount = savedData.totalAmount || 0
        this.totalCount = savedData.totalCount || 0
        this.signInData = savedData.signInData || this.signInData
        this.taskRedPackets = savedData.taskRedPackets || this.taskRedPackets
      }
    },
    
    // 保存红包数据
    saveRedPacketData() {
      const dataToSave = {
        userRedPackets: this.userRedPackets,
        totalAmount: this.totalAmount,
        totalCount: this.totalCount,
        signInData: this.signInData,
        taskRedPackets: this.taskRedPackets
      }
      uni.setStorageSync('redpacket_data', dataToSave)
    },
    
    // 领取红包
    claimRedPacket(packetData) {
      const redPacket = {
        id: Date.now().toString(),
        type: packetData.type,
        amount: packetData.amount,
        title: packetData.title,
        claimTime: new Date().toISOString(),
        source: packetData.source || 'unknown'
      }
      
      this.userRedPackets.push(redPacket)
      this.totalAmount += packetData.amount
      this.totalCount += 1
      
      this.saveRedPacketData()
      
      // 触发红包领取事件
      uni.$emit('redpacket_claimed', redPacket)
    },
    
    // 每日签到
    dailySignIn() {
      const today = new Date().toDateString()
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()
      
      // 检查是否已签到
      if (this.isTodaySignedIn) {
        return { success: false, message: '今日已签到' }
      }
      
      // 更新签到数据
      if (this.signInData.lastSignInDate === yesterday) {
        // 连续签到
        this.signInData.consecutiveDays += 1
      } else {
        // 重新开始签到
        this.signInData.consecutiveDays = 1
      }
      
      this.signInData.lastSignInDate = today
      this.signInData.totalSignInDays += 1
      
      // 获取签到奖励
      const reward = this.getTodayReward
      
      if (reward.type === 'redpacket') {
        // 发放红包
        this.claimRedPacket({
          type: 'signin',
          amount: reward.amount,
          title: '签到红包',
          source: 'daily_signin'
        })
      }
      
      this.saveRedPacketData()
      
      return {
        success: true,
        reward: reward,
        consecutiveDays: this.signInData.consecutiveDays
      }
    },
    
    // 完成任务红包
    completeTaskRedPacket(taskId) {
      const task = this.taskRedPackets.find(t => t.id === taskId)
      if (!task || task.claimed) {
        return { success: false, message: '任务已完成或不存在' }
      }
      
      // 标记任务完成
      task.claimed = true
      task.claimTime = new Date().toISOString()
      
      // 发放红包
      this.claimRedPacket({
        type: 'task',
        amount: task.amount,
        title: task.name,
        source: `task_${taskId}`
      })
      
      this.saveRedPacketData()
      
      return {
        success: true,
        task: task
      }
    },
    
    // 重置每日任务
    resetDailyTasks() {
      this.taskRedPackets.forEach(task => {
        if (task.daily) {
          task.claimed = false
        }
      })
      this.saveRedPacketData()
    },
    
    // 生成随机红包
    generateRandomRedPacket(type = 'special') {
      const config = this.redPacketTypes[type]
      const amount = Math.floor(Math.random() * (config.maxAmount - config.minAmount + 1)) + config.minAmount
      
      return {
        type: type,
        amount: amount,
        title: config.name,
        animation: config.animation,
        color: config.color,
        source: 'random'
      }
    },
    
    // 检查用户行为触发红包
    checkUserBehavior(action, data = {}) {
      switch (action) {
        case 'play_first_game':
          this.completeTaskRedPacket('first_game')
          break
        case 'use_any_tool':
          this.completeTaskRedPacket('use_tool')
          break
        case 'daily_active_5min':
          this.completeTaskRedPacket('daily_active')
          break
      }
    }
  }
})

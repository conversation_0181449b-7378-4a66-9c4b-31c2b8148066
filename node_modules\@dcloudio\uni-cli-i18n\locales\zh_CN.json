{"warning": "警告⚠", "syntaxError": "语法错误❌", "compilerVersion": "编译器版本", "compiling": "正在编译中...", "see": "看", "platform": "平台", "plugin": "插件", "performingHotReload": "正在差量编译...", "cliShared.parseJsonFailed": "解析失败，不符合 json 规范", "cliShared.doesNotExist": "不存在", "cliShared.pagesJsonError": "pages.json 页面配置错误，已被忽略，查看文档: {{0}}", "cliShared.requireReturnJsonObject": "必须返回一个 json 对象", "cliShared.requireExportFunction": "必须导出 function", "cliShared.easycomConflict": "easycom组件冲突：{{0}}", "cliShared.noFoundPlatformPlugin": "缺少平台 {{0}} 插件", "cliShared.extendOnlySupportH5": "目前仅支持基于 h5 平台做扩展", "cliShared.supportPlatform": "{{0}} 支持以下平台 {{1}}", "cliShared.requireConfigUniPlatform": "{{0}} 不存在,必须配置 env->UNI_PLATFORM 基础平台", "cliShared.missingNameAttribute": "{{0}} 缺少 name 属性", "cliShared.missingUniConfig": "{{0}} 缺少 uni.config.js", "migration.errorOnlySupportConvert": "错误: 目前支持 {{0}} 转换", "migration.errorInputNotExists": "错误: '{{0}}' 不存在", "migration.errorCannotConvert": "错误: '{{0}}' 不支持转换", "migration.errorConvertRequireFileUrl": "错误: 单文件转换需要传入 {{0}} 文件地址", "mpWeChat.onlySupportDestructuringSlot": "目前仅支持解构插槽 {{0}}，如 {{1}}", "mpWeChat.slotPropNoSupportReanme": "解构插槽 Prop,不支持将 {{0}} 重命名为 {{1}}，重命名后会影响性能", "uniStat.missingParameter": "缺少 [eventName] 参数", "uniStat.parameterLengthLess": "参数长度不能大于", "uniStat.parameterTypeErrrorString": "参数类型错误,只能为 String 类型", "uniStat.parameterTypeErrrorStringOrObject": "参数类型错误,只能为 String 或 Object 类型", "uniStat.hasTitleOptionString": "参数为 title 时，[options] 参数只能为 String 类型", "templateCompiler.noH5KeyNoSupportExpression": "非 h5 平台 :key 不支持表达式 {{0}}，详情参考: {{1}}", "templateCompiler.notCurrentlySupportScopedSlot": "暂不支持 scoped slot {{0}}", "templateCompiler.idAttribNotAllowInCustomComponentProps": "id 作为属性保留名，不允许在自定义组件 {{0}} 中定义为 props", "templateCompiler.notSupportDynamicSlotName": "{{0}} 不支持动态插槽名，请设置 scopedSlotsCompiler 为 augmented", "templateCompiler.forNestedIndexNameNoArrowRepeat": "{{0}} v-for 嵌套时，索引名称 {{1}} 不允许重复", "templateCompiler.noSupportSyntax": "不支持 {{0}} 语法", "pluginHbuilderx.plaseHXCompileAppPlatform": "请使用 HBuilderX 编译运行至 app-plus 平台", "pluginHbuilderx.hxBuildFailed": "编译失败：HBuilderX 安装目录不能包括 {{0}} 等特殊字符", "pluginHbuilderx.nvueCssWarning": "nvue中不支持如下css。如全局或公共样式受影响，建议将告警样式写在ifndef APP-PLUS-NVUE的条件编译中，详情如下：", "pluginUni.runDebugMode": "请注意运行模式下，因日志输出、sourcemap以及未压缩源码等原因，性能和包体积，均不及发行模式。", "pluginUni.runDebugModeNvue": "尤其是app-nvue的sourcemap影响较大", "pluginUni.runDebugModeMP": "若要正式发布，请点击发行菜单或使用cli发布命令进行发布", "pluginUni.compileToMpPluginOnlySupportPlatform": "编译到小程序插件只支持微信小程序和阿里小程序", "pluginUni.startCompileProjectToPlatform": "开始编译当前项目至 {{0}} {{1}}...", "pluginUni.fileNoExistsCheckAfterRetry": "{{0}} 文件不存在，请检查后重试", "pluginUni.entryDileNoExistsCheckAfterRetry": "{{0}} 入口文件不存在，请检查后重试", "pluginUni.nvueCompileModeForDetail": "当前nvue编译模式：{{0}}。编译模式差异见 {{1}}", "pluginUni.currentProjectDefaultSpaceId": "当前项目的uniCloud使用的默认服务空间spaceId为：{{0}}", "pluginUni.unicloudReleaseH5": "发布web站点需要在uniCloud web控制台操作，绑定安全域名，否则会因为跨域问题而无法访问。教程参考：{{0}}", "pluginUni.unicloudShowedRunByHBuilderX": "当前项目使用了uniCloud，为避免云函数调用跨域问题，建议在HBuilderX内置浏览器里调试，如使用外部浏览器需处理跨域，详见：{{0}}", "pluginUni.pleaseSpecifyPluginName": "请指定插件名", "pluginUni.pluginNameNotExist": "插件名称不存在", "pluginUni.pluginIllegal": "插件不合法", "pluginUni.uniStatisticsNoAppid": "当前应用未配置Appid，无法使用uni统计，详情参考 {{0}}", "pluginUni.uniStatisticsNoVersion": "当前应用未配置uni统计版本，默认使用1.0版本；建议使用uni统计2.0版本，私有部署数据更安全，代码开源可定制。详情：{{0}}", "pluginUni.pleaseConfigScriptName": "请指定 package.json->uni-app->scripts 下的 script 名称", "pluginUni.mpBrowserKernelDifference": "小程序各家浏览器内核及自定义组件实现机制存在差异，可能存在样式布局兼容问题，参考：{{0}}", "mpLoader.firstParameterNeedStaticString": "{{0}}的第一个参数必须为静态字符串", "mpLoader.requireTwoParameter": "{{0}}需要两个参数", "mpLoader.findFail": "{{0}}查找失败", "mpLoader.componentReferenceError": "组件 {{0}} 引用错误", "mpLoader.componentReferenceErrorOnlySupportImport": "组件 {{0}} 引用错误，仅支持 import 方式引入组件", "pagesLoader.pagesNodeCannotNull": "pages.json 中的 pages 节点不能为空", "pagesLoader.nvueFirstPageStartModeIsFast": "App 启动模式: fast, 详见: {{0}}", "pagesLoader.pagesTabbarMinItem2": "{{0}} 需至少包含2项", "pagesLoader.needInPagesNode": "{{0}} 需在 pages 数组中", "i18n.fallbackLocale.default": "当前应用未在 manifest.json 配置 fallbackLocale，默认使用：{{locale}}", "i18n.fallbackLocale.missing": "当前应用配置的 fallbackLocale 或 locale 为：{locale}，但 locale 目录缺少该语言文件", "prompt.run.message": "运行方式：打开 {{devtools}}, 导入 {{outputDir}} 运行。", "prompt.run.devtools.app-plus": "HBuilderX", "prompt.run.devtools.mp-alipay": "支付宝小程序开发者工具", "prompt.run.devtools.mp-baidu": "百度开发者工具", "prompt.run.devtools.mp--kuaishou": "快手开发者工具", "prompt.run.devtools.mp-lark": "飞书开发者工具", "prompt.run.devtools.mp-qq": "QQ小程序开发者工具", "prompt.run.devtools.mp-toutiao": "抖音开发者工具", "prompt.run.devtools.mp-weixin": "微信开发者工具", "prompt.run.devtools.mp-jd": "京东开发者工具", "prompt.run.devtools.mp-xhs": "小红书开发者工具", "prompt.run.devtools.quickapp-webview": "快应用联盟开发者工具 | 华为快应用开发者工具", "prompt.run.devtools.quickapp-webview-huawei": "华为快应用开发者工具", "prompt.run.devtools.quickapp-webview-union": "快应用联盟开发者工具"}
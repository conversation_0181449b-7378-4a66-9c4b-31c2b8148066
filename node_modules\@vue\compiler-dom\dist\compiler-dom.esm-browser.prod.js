function e(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function t(e,t=0,n=e.length){let o=e.split(/(\r?\n)/);const r=o.filter(((e,t)=>t%2==1));o=o.filter(((e,t)=>t%2==0));let s=0;const i=[];for(let c=0;c<o.length;c++)if(s+=o[c].length+(r[c]&&r[c].length||0),s>=t){for(let e=c-2;e<=c+2||n>s;e++){if(e<0||e>=o.length)continue;const l=e+1;i.push(`${l}${" ".repeat(Math.max(3-String(l).length,0))}|  ${o[e]}`);const a=o[e].length,p=r[e]&&r[e].length||0;if(e===c){const e=t-(s-(a+p)),o=Math.max(1,n>s?a-e:n-t);i.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>c){if(n>s){const e=Math.max(Math.min(n-s,a),1);i.push("   |  "+"^".repeat(e))}s+=a+p}}break}return i.join("\n")}const n=/;(?![^(]*\))/g,o=/:([^]+)/,r=/\/\*.*?\*\//gs;const s=e("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),i=e("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),c=e("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),l={},a=()=>{},p=()=>!1,u=/^on[^a-z]/,f=e=>u.test(e),d=Object.assign,h=Array.isArray,m=e=>"string"==typeof e,g=e=>"symbol"==typeof e,y=e=>null!==e&&"object"==typeof e,v=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),b=e("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),S=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},x=/-(\w)/g,k=S((e=>e.replace(x,((e,t)=>t?t.toUpperCase():"")))),N=/\B([A-Z])/g,_=S((e=>e.replace(N,"-$1").toLowerCase())),T=S((e=>e.charAt(0).toUpperCase()+e.slice(1))),E=S((e=>e?`on${T(e)}`:""));function w(e){throw e}function O(e){}function $(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const C=Symbol(""),M=Symbol(""),I=Symbol(""),P=Symbol(""),R=Symbol(""),V=Symbol(""),L=Symbol(""),A=Symbol(""),B=Symbol(""),j=Symbol(""),F=Symbol(""),D=Symbol(""),H=Symbol(""),W=Symbol(""),U=Symbol(""),J=Symbol(""),z=Symbol(""),G=Symbol(""),K=Symbol(""),q=Symbol(""),Z=Symbol(""),Y=Symbol(""),Q=Symbol(""),X=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe=Symbol(""),re=Symbol(""),se=Symbol(""),ie=Symbol(""),ce=Symbol(""),le=Symbol(""),ae=Symbol(""),pe=Symbol(""),ue=Symbol(""),fe=Symbol(""),de=Symbol(""),he=Symbol(""),me={[C]:"Fragment",[M]:"Teleport",[I]:"Suspense",[P]:"KeepAlive",[R]:"BaseTransition",[V]:"openBlock",[L]:"createBlock",[A]:"createElementBlock",[B]:"createVNode",[j]:"createElementVNode",[F]:"createCommentVNode",[D]:"createTextVNode",[H]:"createStaticVNode",[W]:"resolveComponent",[U]:"resolveDynamicComponent",[J]:"resolveDirective",[z]:"resolveFilter",[G]:"withDirectives",[K]:"renderList",[q]:"renderSlot",[Z]:"createSlots",[Y]:"toDisplayString",[Q]:"mergeProps",[X]:"normalizeClass",[ee]:"normalizeStyle",[te]:"normalizeProps",[ne]:"guardReactiveProps",[oe]:"toHandlers",[re]:"camelize",[se]:"capitalize",[ie]:"toHandlerKey",[ce]:"setBlockTracking",[le]:"pushScopeId",[ae]:"popScopeId",[pe]:"withCtx",[ue]:"unref",[fe]:"isRef",[de]:"withMemo",[he]:"isMemoSame"};function ge(e){Object.getOwnPropertySymbols(e).forEach((t=>{me[t]=e[t]}))}const ye={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function ve(e,t=ye){return{type:0,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function be(e,t,n,o,r,s,i,c=!1,l=!1,a=!1,p=ye){return e&&(c?(e.helper(V),e.helper(it(e.inSSR,a))):e.helper(st(e.inSSR,a)),i&&e.helper(G)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:i,isBlock:c,disableTracking:l,isComponent:a,loc:p}}function Se(e,t=ye){return{type:17,loc:t,elements:e}}function xe(e,t=ye){return{type:15,loc:t,properties:e}}function ke(e,t){return{type:16,loc:ye,key:m(e)?Ne(e,!0):e,value:t}}function Ne(e,t=!1,n=ye,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function _e(e,t){return{type:5,loc:t,content:m(e)?Ne(e,!1,t):e}}function Te(e,t=ye){return{type:8,loc:t,children:e}}function Ee(e,t=[],n=ye){return{type:14,loc:n,callee:e,arguments:t}}function we(e,t,n=!1,o=!1,r=ye){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function Oe(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:ye}}function $e(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:ye}}function Ce(e){return{type:21,body:e,loc:ye}}function Me(e){return{type:22,elements:e,loc:ye}}function Ie(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:ye}}function Pe(e,t){return{type:24,left:e,right:t,loc:ye}}function Re(e){return{type:25,expressions:e,loc:ye}}function Ve(e){return{type:26,returns:e,loc:ye}}const Le=e=>4===e.type&&e.isStatic,Ae=(e,t)=>e===t||e===_(t);function Be(e){return Ae(e,"Teleport")?M:Ae(e,"Suspense")?I:Ae(e,"KeepAlive")?P:Ae(e,"BaseTransition")?R:void 0}const je=/^\d|[^\$\w]/,Fe=e=>!je.test(e),De=/[A-Za-z_$\xA0-\uFFFF]/,He=/[\.\?\w$\xA0-\uFFFF]/,We=/\s+[.[]\s*|\s*[.[]\s+/g,Ue=e=>{e=e.trim().replace(We,(e=>e.trim()));let t=0,n=[],o=0,r=0,s=null;for(let i=0;i<e.length;i++){const c=e.charAt(i);switch(t){case 0:if("["===c)n.push(t),t=1,o++;else if("("===c)n.push(t),t=2,r++;else if(!(0===i?De:He).test(c))return!1;break;case 1:"'"===c||'"'===c||"`"===c?(n.push(t),t=3,s=c):"["===c?o++:"]"===c&&(--o||(t=n.pop()));break;case 2:if("'"===c||'"'===c||"`"===c)n.push(t),t=3,s=c;else if("("===c)r++;else if(")"===c){if(i===e.length-1)return!1;--r||(t=n.pop())}break;case 3:c===s&&(t=n.pop(),s=null)}}return!o&&!r},Je=a,ze=Ue;function Ge(e,t,n){const o={source:e.source.slice(t,t+n),start:Ke(e.start,e.source,t),end:e.end};return null!=n&&(o.end=Ke(e.start,e.source,t+n)),o}function Ke(e,t,n=t.length){return qe(d({},e),t,n)}function qe(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function Ze(e,t){if(!e)throw new Error(t||"unexpected compiler condition")}function Ye(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(m(t)?r.name===t:t.test(r.name)))return r}}function Qe(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Xe(s.arg,t))return s}}function Xe(e,t){return!(!e||!Le(e)||e.content!==t)}function et(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function tt(e){return 5===e.type||2===e.type}function nt(e){return 7===e.type&&"slot"===e.name}function ot(e){return 1===e.type&&3===e.tagType}function rt(e){return 1===e.type&&2===e.tagType}function st(e,t){return e||t?B:j}function it(e,t){return e||t?L:A}const ct=new Set([te,ne]);function lt(e,t=[]){if(e&&!m(e)&&14===e.type){const n=e.callee;if(!m(n)&&ct.has(n))return lt(e.arguments[0],t.concat(e))}return[e,t]}function at(e,t,n){let o,r,s=13===e.type?e.props:e.arguments[2],i=[];if(s&&!m(s)&&14===s.type){const e=lt(s);s=e[0],i=e[1],r=i[i.length-1]}if(null==s||m(s))o=xe([t]);else if(14===s.type){const e=s.arguments[0];m(e)||15!==e.type?s.callee===oe?o=Ee(n.helper(Q),[xe([t]),s]):s.arguments.unshift(xe([t])):pt(t,e)||e.properties.unshift(t),!o&&(o=s)}else 15===s.type?(pt(t,s)||s.properties.unshift(t),o=s):(o=Ee(n.helper(Q),[xe([t]),s]),r&&r.callee===ne&&(r=i[i.length-2]));13===e.type?r?r.arguments[0]=o:e.props=o:r?r.arguments[0]=o:e.arguments[2]=o}function pt(e,t){let n=!1;if(4===e.key.type){const o=e.key.content;n=t.properties.some((e=>4===e.key.type&&e.key.content===o))}return n}function ut(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}function ft(e,t){if(!e||0===Object.keys(t).length)return!1;switch(e.type){case 1:for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&(ft(o.arg,t)||ft(o.exp,t)))return!0}return e.children.some((e=>ft(e,t)));case 11:return!!ft(e.source,t)||e.children.some((e=>ft(e,t)));case 9:return e.branches.some((e=>ft(e,t)));case 10:return!!ft(e.condition,t)||e.children.some((e=>ft(e,t)));case 4:return!e.isStatic&&Fe(e.content)&&!!t[e.content];case 8:return e.children.some((e=>y(e)&&ft(e,t)));case 5:case 12:return ft(e.content,t);default:return!1}}function dt(e){return 14===e.type&&e.callee===de?e.arguments[1].returns:e}function ht(e,{helper:t,removeHelper:n,inSSR:o}){e.isBlock||(e.isBlock=!0,n(st(o,e.isComponent)),t(V),t(it(o,e.isComponent)))}const mt={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_PROP:{message:".prop modifier for v-bind has been removed and no longer necessary. Vue 3 will automatically set a binding as DOM property when appropriate."},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTER:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function gt(e,t){const n=t.options?t.options.compatConfig:t.compatConfig,o=n&&n[e];return"MODE"===e?o||3:o}function yt(e,t){const n=gt("MODE",t),o=gt(e,t);return 3===n?!0===o:!1!==o}function vt(e,t,n,...o){return yt(e,t)}function bt(e,t,n,...o){if("suppress-warning"===gt(e,t))return;const{message:r,link:s}=mt[e],i=`(deprecation ${e}) ${"function"==typeof r?r(...o):r}${s?`\n  Details: ${s}`:""}`,c=new SyntaxError(i);c.code=e,n&&(c.loc=n),t.onWarn(c)}const St=/&(gt|lt|amp|apos|quot);/g,xt={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},kt={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:p,isPreTag:p,isCustomElement:p,decodeEntities:e=>e.replace(St,((e,t)=>xt[t])),onError:w,onWarn:O,comments:!1};function Nt(e,t={}){const n=function(e,t){const n=d({},kt);let o;for(o in t)n[o]=void 0===t[o]?kt[o]:t[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1,onWarn:n.onWarn}}(e,t),o=At(n);return ve(_t(n,0,[]),Bt(n,o))}function _t(e,t,n){const o=jt(n),r=o?o.ns:0,s=[];for(;!Ut(e,t,n);){const i=e.source;let c;if(0===t||1===t)if(!e.inVPre&&Ft(i,e.options.delimiters[0]))c=Rt(e,t);else if(0===t&&"<"===i[0])if(1===i.length);else if("!"===i[1])c=Ft(i,"\x3c!--")?wt(e):Ft(i,"<!DOCTYPE")?Ot(e):Ft(i,"<![CDATA[")&&0!==r?Et(e,n):Ot(e);else if("/"===i[1])if(2===i.length);else{if(">"===i[2]){Dt(e,3);continue}if(/[a-z]/i.test(i[2])){Mt(e,1,o);continue}c=Ot(e)}else/[a-z]/i.test(i[1])?(c=$t(e,n),yt("COMPILER_NATIVE_TEMPLATE",e)&&c&&"template"===c.tag&&!c.props.some((e=>7===e.type&&Ct(e.name)))&&(c=c.children)):"?"===i[1]&&(c=Ot(e));if(c||(c=Vt(e,t)),h(c))for(let e=0;e<c.length;e++)Tt(s,c[e]);else Tt(s,c)}let i=!1;if(2!==t&&1!==t){const t="preserve"!==e.options.whitespace;for(let n=0;n<s.length;n++){const o=s[n];if(2===o.type)if(e.inPre)o.content=o.content.replace(/\r\n/g,"\n");else if(/[^\t\r\n\f ]/.test(o.content))t&&(o.content=o.content.replace(/[\t\r\n\f ]+/g," "));else{const e=s[n-1],r=s[n+1];!e||!r||t&&(3===e.type&&3===r.type||3===e.type&&1===r.type||1===e.type&&3===r.type||1===e.type&&1===r.type&&/[\r\n]/.test(o.content))?(i=!0,s[n]=null):o.content=" "}else 3!==o.type||e.options.comments||(i=!0,s[n]=null)}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return i?s.filter(Boolean):s}function Tt(e,t){if(2===t.type){const n=jt(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function Et(e,t){Dt(e,9);const n=_t(e,3,t);return 0===e.source.length||Dt(e,3),n}function wt(e){const t=At(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)Dt(e,s-r+1),r=s+1;Dt(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),Dt(e,e.source.length);return{type:3,content:n,loc:Bt(e,t)}}function Ot(e){const t=At(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),Dt(e,e.source.length)):(o=e.source.slice(n,r),Dt(e,r+1)),{type:3,content:o,loc:Bt(e,t)}}function $t(e,t){const n=e.inPre,o=e.inVPre,r=jt(t),s=Mt(e,0,r),i=e.inPre&&!n,c=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return i&&(e.inPre=!1),c&&(e.inVPre=!1),s;t.push(s);const l=e.options.getTextMode(s,r),a=_t(e,l,t);t.pop();{const t=s.props.find((e=>6===e.type&&"inline-template"===e.name));if(t&&vt("COMPILER_INLINE_TEMPLATE",e)){const n=Bt(e,s.loc.end);t.value={type:2,content:n.source,loc:n}}}if(s.children=a,Jt(e.source,s.tag))Mt(e,1,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=a[0];e&&Ft(e.loc.source,"\x3c!--")}return s.loc=Bt(e,s.loc.start),i&&(e.inPre=!1),c&&(e.inVPre=!1),s}const Ct=e("if,else,else-if,for,slot");function Mt(e,t,n){const o=At(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],i=e.options.getNamespace(s,n);Dt(e,r[0].length),Ht(e);const c=At(e),l=e.source;e.options.isPreTag(s)&&(e.inPre=!0);let a=It(e,t);0===t&&!e.inVPre&&a.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,d(e,c),e.source=l,a=It(e,t).filter((e=>"v-pre"!==e.name)));let p=!1;if(0===e.source.length||(p=Ft(e.source,"/>"),Dt(e,p?2:1)),1===t)return;let u=0;return e.inVPre||("slot"===s?u=2:"template"===s?a.some((e=>7===e.type&&Ct(e.name)))&&(u=3):function(e,t,n){const o=n.options;if(o.isCustomElement(e))return!1;if("component"===e||/^[A-Z]/.test(e)||Be(e)||o.isBuiltInComponent&&o.isBuiltInComponent(e)||o.isNativeTag&&!o.isNativeTag(e))return!0;for(let r=0;r<t.length;r++){const e=t[r];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(vt("COMPILER_IS_ON_ELEMENT",n))return!0}}else{if("is"===e.name)return!0;if("bind"===e.name&&Xe(e.arg,"is")&&vt("COMPILER_IS_ON_ELEMENT",n))return!0}}}(s,a,e)&&(u=1)),{type:1,ns:i,tag:s,tagType:u,props:a,isSelfClosing:p,children:[],loc:Bt(e,o),codegenNode:void 0}}function It(e,t){const n=[],o=new Set;for(;e.source.length>0&&!Ft(e.source,">")&&!Ft(e.source,"/>");){if(Ft(e.source,"/")){Dt(e,1),Ht(e);continue}const r=Pt(e,o);6===r.type&&r.value&&"class"===r.name&&(r.value.content=r.value.content.replace(/\s+/g," ").trim()),0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),Ht(e)}return n}function Pt(e,t){const n=At(e),o=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(o),t.add(o);{const e=/["'<]/g;let t;for(;t=e.exec(o););}let r;Dt(e,o.length),/^[\t\r\n\f ]*=/.test(e.source)&&(Ht(e),Dt(e,1),Ht(e),r=function(e){const t=At(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){Dt(e,1);const t=e.source.indexOf(o);-1===t?n=Lt(e,e.source.length,4):(n=Lt(e,t,4),Dt(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]););n=Lt(e,t[0].length,4)}return{content:n,isQuoted:r,loc:Bt(e,t)}}(e));const s=Bt(e,n);if(!e.inVPre&&/^(v-[A-Za-z0-9-]|:|\.|@|#)/.test(o)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^\.|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(o);let i,c=Ft(o,"."),l=t[1]||(c||Ft(o,":")?"bind":Ft(o,"@")?"on":"slot");if(t[2]){const r="slot"===l,s=o.lastIndexOf(t[2]),c=Bt(e,Wt(e,n,s),Wt(e,n,s+t[2].length+(r&&t[3]||"").length));let a=t[2],p=!0;a.startsWith("[")?(p=!1,a=a.endsWith("]")?a.slice(1,a.length-1):a.slice(1)):r&&(a+=t[3]||""),i={type:4,content:a,isStatic:p,constType:p?3:0,loc:c}}if(r&&r.isQuoted){const e=r.loc;e.start.offset++,e.start.column++,e.end=Ke(e.start,r.content),e.source=e.source.slice(1,-1)}const a=t[3]?t[3].slice(1).split("."):[];return c&&a.push("prop"),"bind"===l&&i&&a.includes("sync")&&vt("COMPILER_V_BIND_SYNC",e,0)&&(l="model",a.splice(a.indexOf("sync"),1)),{type:7,name:l,exp:r&&{type:4,content:r.content,isStatic:!1,constType:0,loc:r.loc},arg:i,modifiers:a,loc:s}}return!e.inVPre&&Ft(o,"v-"),{type:6,name:o,value:r&&{type:2,content:r.content,loc:r.loc},loc:s}}function Rt(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=At(e);Dt(e,n.length);const i=At(e),c=At(e),l=r-n.length,a=e.source.slice(0,l),p=Lt(e,l,t),u=p.trim(),f=p.indexOf(u);f>0&&qe(i,a,f);return qe(c,a,l-(p.length-u.length-f)),Dt(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:u,loc:Bt(e,i,c)},loc:Bt(e,s)}}function Vt(e,t){const n=3===t?["]]>"]:["<",e.options.delimiters[0]];let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=At(e);return{type:2,content:Lt(e,o,t),loc:Bt(e,r)}}function Lt(e,t,n){const o=e.source.slice(0,t);return Dt(e,t),2!==n&&3!==n&&o.includes("&")?e.options.decodeEntities(o,4===n):o}function At(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function Bt(e,t,n){return{start:t,end:n=n||At(e),source:e.originalSource.slice(t.offset,n.offset)}}function jt(e){return e[e.length-1]}function Ft(e,t){return e.startsWith(t)}function Dt(e,t){const{source:n}=e;qe(e,n,t),e.source=n.slice(t)}function Ht(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&Dt(e,t[0].length)}function Wt(e,t,n){return Ke(t,e.originalSource.slice(t.offset,n),n)}function Ut(e,t,n){const o=e.source;switch(t){case 0:if(Ft(o,"</"))for(let e=n.length-1;e>=0;--e)if(Jt(o,n[e].tag))return!0;break;case 1:case 2:{const e=jt(n);if(e&&Jt(o,e.tag))return!0;break}case 3:if(Ft(o,"]]>"))return!0}return!o}function Jt(e,t){return Ft(e,"</")&&e.slice(2,2+t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function zt(e,t){Kt(e,t,Gt(e,e.children[0]))}function Gt(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!rt(t)}function Kt(e,t,n=!1){const{children:o}=e,r=o.length;let s=0;for(let i=0;i<o.length;i++){const e=o[i];if(1===e.type&&0===e.tagType){const o=n?0:qt(e,t);if(o>0){if(o>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),s++;continue}}else{const n=e.codegenNode;if(13===n.type){const o=en(n);if((!o||512===o||1===o)&&Qt(e,t)>=2){const o=Xt(e);o&&(n.props=t.hoist(o))}n.dynamicProps&&(n.dynamicProps=t.hoist(n.dynamicProps))}}}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,Kt(e,t),n&&t.scopes.vSlot--}else if(11===e.type)Kt(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)Kt(e.branches[n],t,1===e.branches[n].children.length)}s&&t.transformHoist&&t.transformHoist(o,t,e),s&&s===r&&1===e.type&&0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&h(e.codegenNode.children)&&(e.codegenNode.children=t.hoist(Se(e.codegenNode.children)))}function qt(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(r.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag)return 0;if(en(r))return n.set(e,0),0;{let o=3;const s=Qt(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=qt(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=qt(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}if(r.isBlock){for(let t=0;t<e.props.length;t++){if(7===e.props[t].type)return n.set(e,0),0}t.removeHelper(V),t.removeHelper(it(t.inSSR,r.isComponent)),r.isBlock=!1,t.helper(st(t.inSSR,r.isComponent))}return n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return qt(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(m(o)||g(o))continue;const r=qt(o,t);if(0===r)return 0;r<s&&(s=r)}return s}}const Zt=new Set([X,ee,te,ne]);function Yt(e,t){if(14===e.type&&!m(e.callee)&&Zt.has(e.callee)){const n=e.arguments[0];if(4===n.type)return qt(n,t);if(14===n.type)return Yt(n,t)}return 0}function Qt(e,t){let n=3;const o=Xt(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],i=qt(r,t);if(0===i)return i;let c;if(i<n&&(n=i),c=4===s.type?qt(s,t):14===s.type?Yt(s,t):0,0===c)return c;c<n&&(n=c)}}return n}function Xt(e){const t=e.codegenNode;if(13===t.type)return t.props}function en(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function tn(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:o=!1,cacheHandlers:r=!1,nodeTransforms:s=[],directiveTransforms:i={},transformHoist:c=null,isBuiltInComponent:p=a,isCustomElement:u=a,expressionPlugins:f=[],scopeId:d=null,slotted:h=!0,ssr:g=!1,inSSR:y=!1,ssrCssVars:v="",bindingMetadata:b=l,inline:S=!1,isTS:x=!1,onError:N=w,onWarn:_=O,compatConfig:E}){const $=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),C={selfName:$&&T(k($[1])),prefixIdentifiers:n,hoistStatic:o,cacheHandlers:r,nodeTransforms:s,directiveTransforms:i,transformHoist:c,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:f,scopeId:d,slotted:h,ssr:g,inSSR:y,ssrCssVars:v,bindingMetadata:b,inline:S,isTS:x,onError:N,onWarn:_,compatConfig:E,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=C.helpers.get(e)||0;return C.helpers.set(e,t+1),e},removeHelper(e){const t=C.helpers.get(e);if(t){const n=t-1;n?C.helpers.set(e,n):C.helpers.delete(e)}},helperString:e=>`_${me[C.helper(e)]}`,replaceNode(e){C.parent.children[C.childIndex]=C.currentNode=e},removeNode(e){const t=e?C.parent.children.indexOf(e):C.currentNode?C.childIndex:-1;e&&e!==C.currentNode?C.childIndex>t&&(C.childIndex--,C.onNodeRemoved()):(C.currentNode=null,C.onNodeRemoved()),C.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){m(e)&&(e=Ne(e)),C.hoists.push(e);const t=Ne(`_hoisted_${C.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>$e(C.cached++,e,t)};return C.filters=new Set,C}function nn(e,t){const n=tn(e,t);on(e,n),t.hoistStatic&&zt(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:o}=e;if(1===o.length){const n=o[0];if(Gt(e,n)&&n.codegenNode){const o=n.codegenNode;13===o.type&&ht(o,t),e.codegenNode=o}else e.codegenNode=n}else if(o.length>1){let o=64;e.codegenNode=be(t,n(C),void 0,e.children,o+"",void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.filters=[...n.filters]}function on(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(h(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(F);break;case 5:t.ssr||t.helper(Y);break;case 9:for(let n=0;n<e.branches.length;n++)on(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];m(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,on(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function rn(e,t){const n=m(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(nt))return;const s=[];for(let i=0;i<r.length;i++){const c=r[i];if(7===c.type&&n(c.name)){r.splice(i,1),i--;const n=t(e,c,o);n&&s.push(n)}}return s}}}const sn=e=>`${me[e]}: _${me[e]}`;function cn(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:i=!1,runtimeGlobalName:c="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:a="vue/server-renderer",ssr:p=!1,isTS:u=!1,inSSR:f=!1}){const d={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:i,runtimeGlobalName:c,runtimeModuleName:l,ssrRuntimeModuleName:a,ssr:p,isTS:u,inSSR:f,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${me[e]}`,push(e,t){d.code+=e},indent(){h(++d.indentLevel)},deindent(e=!1){e?--d.indentLevel:h(--d.indentLevel)},newline(){h(d.indentLevel)}};function h(e){d.push("\n"+"  ".repeat(e))}return d}function ln(e,t={}){const n=cn(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:i,deindent:c,newline:l,ssr:a}=n,p=Array.from(e.helpers),u=p.length>0,f=!s&&"module"!==o,d=n;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,i=Array.from(e.helpers);if(i.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[B,j,F,D,H].filter((e=>i.includes(e))).map(sn).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o();for(let r=0;r<e.length;r++){const s=e[r];s&&(n(`const _hoisted_${r+1} = `),fn(s,t),o())}t.pure=!1})(e.hoists,t),o(),n("return ")}(e,d);if(r(`function ${a?"ssrRender":"render"}(${(a?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),f&&(r("with (_ctx) {"),i(),u&&(r(`const { ${p.map(sn).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(an(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(an(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),an(e.filters,"filter",n),l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),a||r("return "),e.codegenNode?fn(e.codegenNode,n):r("null"),f&&(c(),r("}")),c(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function an(e,t,{helper:n,push:o,newline:r,isTS:s}){const i=n("filter"===t?z:"component"===t?W:J);for(let c=0;c<e.length;c++){let n=e[c];const l=n.endsWith("__self");l&&(n=n.slice(0,-6)),o(`const ${ut(n,t)} = ${i}(${JSON.stringify(n)}${l?", true":""})${s?"!":""}`),c<e.length-1&&r()}}function pn(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),un(e,t,n),n&&t.deindent(),t.push("]")}function un(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let i=0;i<e.length;i++){const c=e[i];m(c)?r(c):h(c)?pn(c,t):fn(c,t),i<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function fn(e,t){if(m(e))t.push(e);else if(g(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:fn(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:dn(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n("/*#__PURE__*/");n(`${o(Y)}(`),fn(e.content,t),n(")")}(e,t);break;case 8:hn(e,t);break;case 3:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n("/*#__PURE__*/");n(`${o(F)}(${JSON.stringify(e.content)})`,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:i,children:c,patchFlag:l,dynamicProps:a,directives:p,isBlock:u,disableTracking:f,isComponent:d}=e;p&&n(o(G)+"(");u&&n(`(${o(V)}(${f?"true":""}), `);r&&n("/*#__PURE__*/");const h=u?it(t.inSSR,d):st(t.inSSR,d);n(o(h)+"(",e),un(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,i,c,l,a]),t),n(")"),u&&n(")");p&&(n(", "),fn(p,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=m(e.callee)?e.callee:o(e.callee);r&&n("/*#__PURE__*/");n(s+"(",e),un(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:i}=e;if(!i.length)return void n("{}",e);const c=i.length>1||!1;n(c?"{":"{ "),c&&o();for(let l=0;l<i.length;l++){const{key:e,value:o}=i[l];mn(e,t),n(": "),fn(o,t),l<i.length-1&&(n(","),s())}c&&r(),n(c?"}":" }")}(e,t);break;case 17:!function(e,t){pn(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:i,body:c,newline:l,isSlot:a}=e;a&&n(`_${me[pe]}(`);n("(",e),h(s)?un(s,t):s&&fn(s,t);n(") => "),(l||c)&&(n("{"),o());i?(l&&n("return "),h(i)?pn(i,t):fn(i,t)):c&&fn(c,t);(l||c)&&(r(),n("}"));a&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:i,indent:c,deindent:l,newline:a}=t;if(4===n.type){const e=!Fe(n.content);e&&i("("),dn(n,t),e&&i(")")}else i("("),fn(n,t),i(")");s&&c(),t.indentLevel++,s||i(" "),i("? "),fn(o,t),t.indentLevel--,s&&a(),s||i(" "),i(": ");const p=19===r.type;p||t.indentLevel++;fn(r,t),p||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:i}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(ce)}(-1),`),i());n(`_cache[${e.index}] = `),fn(e.value,t),e.isVNode&&(n(","),i(),n(`${o(ce)}(1),`),i(),n(`_cache[${e.index}]`),s());n(")")}(e,t);break;case 21:un(e.body,t,!0,!1)}}function dn(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function hn(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];m(o)?t.push(o):fn(o,t)}}function mn(e,t){const{push:n}=t;if(8===e.type)n("["),hn(e,t),n("]");else if(e.isStatic){n(Fe(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}function gn(e,t,n=!1,o=[],r=Object.create(null)){}function yn(e,t,n){return!1}function vn(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){const n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1}function bn(e,t){for(const n of e.params)for(const e of xn(n))t(e)}function Sn(e,t){for(const n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(const e of n.declarations)for(const n of xn(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}}function xn(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(const o of e.properties)xn("RestElement"===o.type?o.argument:o.value,t);break;case"ArrayPattern":e.elements.forEach((e=>{e&&xn(e,t)}));break;case"RestElement":xn(e.argument,t);break;case"AssignmentPattern":xn(e.left,t)}return t}const kn=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),Nn=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed,_n=(e,t)=>Nn(t)&&t.key===e,Tn=(e,t)=>{if(5===e.type)e.content=En(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=En(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=En(n,t))}}};function En(e,t,n=!1,o=!1,r=Object.create(t.identifiers)){return e}function wn(e){return m(e)?e:4===e.type?e.content:e.children.map(wn).join("")}const On=rn(/^(if|else|else-if)$/,((e,t,n)=>$n(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),i=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(o)e.codegenNode=Mn(t,i,n);else{const o=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);o.alternate=Mn(t,i+e.branches.length-1,n)}}}))));function $n(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){t.exp=Ne("true",!1,t.exp?t.exp.loc:e.loc)}if("if"===t.name){const r=Cn(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const i=r[s];if(i&&3===i.type)n.removeNode(i);else{if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){n.removeNode();const r=Cn(e,t);i.branches.push(r);const s=o&&o(i,r,!1);on(r,n),s&&s(),n.currentNode=null}break}n.removeNode(i)}}}}function Cn(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!Ye(e,"for")?e.children:[e],userKey:Qe(e,"key"),isTemplateIf:n}}function Mn(e,t,n){return e.condition?Oe(e.condition,In(e,t,n),Ee(n.helper(F),['""',"true"])):In(e,t,n)}function In(e,t,n){const{helper:o}=n,r=ke("key",Ne(`${t}`,!1,ye,2)),{children:s}=e,i=s[0];if(1!==s.length||1!==i.type){if(1===s.length&&11===i.type){const e=i.codegenNode;return at(e,r,n),e}{let t=64;return be(n,o(C),xe([r]),s,t+"",void 0,void 0,!0,!1,!1,e.loc)}}{const e=i.codegenNode,t=dt(e);return 13===t.type&&ht(t,n),at(t,r,n),e}}const Pn=rn("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return Rn(e,t,n,(t=>{const s=Ee(o(K),[t.source]),i=ot(e),c=Ye(e,"memo"),l=Qe(e,"key"),a=l&&(6===l.type?Ne(l.value.content,!0):l.exp),p=l?ke("key",a):null,u=4===t.source.type&&t.source.constType>0,f=u?64:l?128:256;return t.codegenNode=be(n,o(C),void 0,s,f+"",void 0,void 0,!0,!u,!1,e.loc),()=>{let l;const{children:f}=t,d=1!==f.length||1!==f[0].type,h=rt(e)?e:i&&1===e.children.length&&rt(e.children[0])?e.children[0]:null;if(h?(l=h.codegenNode,i&&p&&at(l,p,n)):d?l=be(n,o(C),p?xe([p]):void 0,e.children,"64",void 0,void 0,!0,void 0,!1):(l=f[0].codegenNode,i&&p&&at(l,p,n),l.isBlock!==!u&&(l.isBlock?(r(V),r(it(n.inSSR,l.isComponent))):r(st(n.inSSR,l.isComponent))),l.isBlock=!u,l.isBlock?(o(V),o(it(n.inSSR,l.isComponent))):o(st(n.inSSR,l.isComponent))),c){const e=we(Fn(t.parseResult,[Ne("_cached")]));e.body=Ce([Te(["const _memo = (",c.exp,")"]),Te(["if (_cached",...a?[" && _cached.key === ",a]:[],` && ${n.helperString(he)}(_cached, _memo)) return _cached`]),Te(["const _item = ",l]),Ne("_item.memo = _memo"),Ne("return _item")]),s.arguments.push(e,Ne("_cache"),Ne(String(n.cached++)))}else s.arguments.push(we(Fn(t.parseResult),l,!0))}}))}));function Rn(e,t,n,o){if(!t.exp)return;const r=Bn(t.exp);if(!r)return;const{scopes:s}=n,{source:i,value:c,key:l,index:a}=r,p={type:11,loc:t.loc,source:i,valueAlias:c,keyAlias:l,objectIndexAlias:a,parseResult:r,children:ot(e)?e.children:[e]};n.replaceNode(p),s.vFor++;const u=o&&o(p);return()=>{s.vFor--,u&&u()}}const Vn=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Ln=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,An=/^\(|\)$/g;function Bn(e,t){const n=e.loc,o=e.content,r=o.match(Vn);if(!r)return;const[,s,i]=r,c={source:jn(n,i.trim(),o.indexOf(i,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(An,"").trim();const a=s.indexOf(l),p=l.match(Ln);if(p){l=l.replace(Ln,"").trim();const e=p[1].trim();let t;if(e&&(t=o.indexOf(e,a+l.length),c.key=jn(n,e,t)),p[2]){const r=p[2].trim();r&&(c.index=jn(n,r,o.indexOf(r,c.key?t+e.length:a+l.length)))}}return l&&(c.value=jn(n,l,a)),c}function jn(e,t,n){return Ne(t,!1,Ge(e,n,t.length))}function Fn({value:e,key:t,index:n},o=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||Ne("_".repeat(t+1),!1)))}([e,t,n,...o])}const Dn=Ne("undefined",!1),Hn=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Ye(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},Wn=(e,t)=>{let n;if(ot(e)&&e.props.some(nt)&&(n=Ye(e,"for"))){const e=n.parseResult=Bn(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:i}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&i(n),o&&i(o),r&&i(r)}}}},Un=(e,t,n)=>we(e,t,!1,!0,t.length?t[0].loc:n);function Jn(e,t,n=Un){t.helper(pe);const{children:o,loc:r}=e,s=[],i=[];let c=t.scopes.vSlot>0||t.scopes.vFor>0;const l=Ye(e,"slot",!0);if(l){const{arg:e,exp:t}=l;e&&!Le(e)&&(c=!0),s.push(ke(e||Ne("default",!0),n(t,o,r)))}let a=!1,p=!1;const u=[],f=new Set;let d=0;for(let g=0;g<o.length;g++){const e=o[g];let r;if(!ot(e)||!(r=Ye(e,"slot",!0))){3!==e.type&&u.push(e);continue}if(l)break;a=!0;const{children:h,loc:m}=e,{arg:y=Ne("default",!0),exp:v}=r;let b;Le(y)?b=y?y.content:"default":c=!0;const S=n(v,h,m);let x,k,N;if(x=Ye(e,"if"))c=!0,i.push(Oe(x.exp,zn(y,S,d++),Dn));else if(k=Ye(e,/^else(-if)?$/,!0)){let e,t=g;for(;t--&&(e=o[t],3===e.type););if(e&&ot(e)&&Ye(e,"if")){o.splice(g,1),g--;let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=k.exp?Oe(k.exp,zn(y,S,d++),Dn):zn(y,S,d++)}}else if(N=Ye(e,"for")){c=!0;const e=N.parseResult||Bn(N.exp);e&&i.push(Ee(t.helper(K),[e.source,we(Fn(e),zn(y,S),!0)]))}else{if(b){if(f.has(b))continue;f.add(b),"default"===b&&(p=!0)}s.push(ke(y,S))}}if(!l){const e=(e,o)=>{const s=n(e,o,r);return t.compatConfig&&(s.isNonScopedSlot=!0),ke("default",s)};a?u.length&&u.some((e=>Kn(e)))&&(p||s.push(e(void 0,u))):s.push(e(void 0,o))}const h=c?2:Gn(e.children)?3:1;let m=xe(s.concat(ke("_",Ne(h+"",!1))),r);return i.length&&(m=Ee(t.helper(Z),[m,Se(i)])),{slots:m,hasDynamicSlots:c}}function zn(e,t,n){const o=[ke("name",e),ke("fn",t)];return null!=n&&o.push(ke("key",Ne(String(n),!0))),xe(o)}function Gn(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||Gn(n.children))return!0;break;case 9:if(Gn(n.branches))return!0;break;case 10:case 11:if(Gn(n.children))return!0}}return!1}function Kn(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():Kn(e.content))}const qn=new WeakMap,Zn=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType;let s=r?Yn(e,t):`"${n}"`;const i=y(s)&&s.callee===U;let c,l,a,p,u,f,d=0,h=i||s===M||s===I||!r&&("svg"===n||"foreignObject"===n);if(o.length>0){const n=Qn(e,t,void 0,r,i);c=n.props,d=n.patchFlag,u=n.dynamicPropNames;const o=n.directives;f=o&&o.length?Se(o.map((e=>to(e,t)))):void 0,n.shouldUseBlock&&(h=!0)}if(e.children.length>0){s===P&&(h=!0,d|=1024);if(r&&s!==M&&s!==P){const{slots:n,hasDynamicSlots:o}=Jn(e,t);l=n,o&&(d|=1024)}else if(1===e.children.length&&s!==M){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===qt(n,t)&&(d|=1),l=r||2===o?n:e.children}else l=e.children}0!==d&&(a=String(d),u&&u.length&&(p=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(u))),e.codegenNode=be(t,s,c,l,a,p,f,!!h,!1,r,e.loc)};function Yn(e,t,n=!1){let{tag:o}=e;const r=no(o),s=Qe(e,"is");if(s)if(r||yt("COMPILER_IS_ON_ELEMENT",t)){const e=6===s.type?s.value&&Ne(s.value.content,!0):s.exp;if(e)return Ee(t.helper(U),[e])}else 6===s.type&&s.value.content.startsWith("vue:")&&(o=s.value.content.slice(4));const i=!r&&Ye(e,"is");if(i&&i.exp)return Ee(t.helper(U),[i.exp]);const c=Be(o)||t.isBuiltInComponent(o);return c?(n||t.helper(c),c):(t.helper(W),t.components.add(o),ut(o,"component"))}function Qn(e,t,n=e.props,o,r,s=!1){const{tag:i,loc:c,children:l}=e;let a=[];const p=[],u=[],d=l.length>0;let h=!1,m=0,y=!1,S=!1,x=!1,k=!1,N=!1,_=!1;const T=[],E=e=>{a.length&&(p.push(xe(Xn(a),c)),a=[]),e&&p.push(e)},w=({key:e,value:n})=>{if(Le(e)){const s=e.content,i=f(s);if(!i||o&&!r||"onclick"===s.toLowerCase()||"onUpdate:modelValue"===s||v(s)||(k=!0),i&&v(s)&&(_=!0),20===n.type||(4===n.type||8===n.type)&&qt(n,t)>0)return;"ref"===s?y=!0:"class"===s?S=!0:"style"===s?x=!0:"key"===s||T.includes(s)||T.push(s),!o||"class"!==s&&"style"!==s||T.includes(s)||T.push(s)}else N=!0};for(let f=0;f<n.length;f++){const r=n[f];if(6===r.type){const{loc:e,name:n,value:o}=r;let s=!0;if("ref"===n&&(y=!0,t.scopes.vFor>0&&a.push(ke(Ne("ref_for",!0),Ne("true")))),"is"===n&&(no(i)||o&&o.content.startsWith("vue:")||yt("COMPILER_IS_ON_ELEMENT",t)))continue;a.push(ke(Ne(n,!0,Ge(e,0,n.length)),Ne(o?o.content:"",s,o?o.loc:e)))}else{const{name:n,arg:l,exp:f,loc:m}=r,y="bind"===n,v="on"===n;if("slot"===n)continue;if("once"===n||"memo"===n)continue;if("is"===n||y&&Xe(l,"is")&&(no(i)||yt("COMPILER_IS_ON_ELEMENT",t)))continue;if(v&&s)continue;if((y&&Xe(l,"key")||v&&d&&Xe(l,"vue:before-update"))&&(h=!0),y&&Xe(l,"ref")&&t.scopes.vFor>0&&a.push(ke(Ne("ref_for",!0),Ne("true"))),!l&&(y||v)){if(N=!0,f)if(y){if(E(),yt("COMPILER_V_BIND_OBJECT_ORDER",t)){p.unshift(f);continue}p.push(f)}else E({type:14,loc:m,callee:t.helper(oe),arguments:o?[f]:[f,"true"]});continue}const S=t.directiveTransforms[n];if(S){const{props:n,needRuntime:o}=S(r,e,t);!s&&n.forEach(w),v&&l&&!Le(l)?E(xe(n,c)):a.push(...n),o&&(u.push(r),g(o)&&qn.set(r,o))}else b(n)||(u.push(r),d&&(h=!0))}}let O;if(p.length?(E(),O=p.length>1?Ee(t.helper(Q),p,c):p[0]):a.length&&(O=xe(Xn(a),c)),N?m|=16:(S&&!o&&(m|=2),x&&!o&&(m|=4),T.length&&(m|=8),k&&(m|=32)),h||0!==m&&32!==m||!(y||_||u.length>0)||(m|=512),!t.inSSR&&O)switch(O.type){case 15:let e=-1,n=-1,o=!1;for(let t=0;t<O.properties.length;t++){const r=O.properties[t].key;Le(r)?"class"===r.content?e=t:"style"===r.content&&(n=t):r.isHandlerKey||(o=!0)}const r=O.properties[e],s=O.properties[n];o?O=Ee(t.helper(te),[O]):(r&&!Le(r.value)&&(r.value=Ee(t.helper(X),[r.value])),s&&(x||4===s.value.type&&"["===s.value.content.trim()[0]||17===s.value.type)&&(s.value=Ee(t.helper(ee),[s.value])));break;case 14:break;default:O=Ee(t.helper(te),[Ee(t.helper(ne),[O])])}return{props:O,directives:u,patchFlag:m,dynamicPropNames:T,shouldUseBlock:h}}function Xn(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,i=t.get(s);i?("style"===s||"class"===s||f(s))&&eo(i,r):(t.set(s,r),n.push(r))}return n}function eo(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=Se([e.value,t.value],e.loc)}function to(e,t){const n=[],o=qn.get(e);o?n.push(t.helperString(o)):(t.helper(J),t.directives.add(e.name),n.push(ut(e.name,"directive")));const{loc:r}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=Ne("true",!1,r);n.push(xe(e.modifiers.map((e=>ke(e,t))),r))}return Se(n,e.loc)}function no(e){return"component"===e||"Component"===e}const oo=(e,t)=>{if(rt(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=ro(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r,"{}","undefined","true"];let c=2;s&&(i[2]=s,c=3),n.length&&(i[3]=we([],n,!1,!1,o),c=4),t.scopeId&&!t.slotted&&(c=5),i.splice(c),e.codegenNode=Ee(t.helper(q),i,o)}};function ro(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=k(t.name),r.push(t))):"bind"===t.name&&Xe(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&Le(t.arg)&&(t.arg.content=k(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=Qn(e,t,r,!1,!1);n=o}return{slotName:o,slotProps:n}}const so=/^\s*([\w$_]+|(async\s*)?\([^)]*?\))\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,io=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:i}=e;let c;if(4===i.type)if(i.isStatic){let e=i.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`);c=Ne(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?E(k(e)):`on:${e}`,!0,i.loc)}else c=Te([`${n.helperString(ie)}(`,i,")"]);else c=i,c.children.unshift(`${n.helperString(ie)}(`),c.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let a=n.cacheHandlers&&!l&&!n.inVOnce;if(l){const e=ze(l.content),t=!(e||so.test(l.content)),n=l.content.includes(";");(t||a&&e)&&(l=Te([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let p={props:[ke(c,l||Ne("() => {}",!1,r))]};return o&&(p=o(p)),a&&(p.props[0].value=n.cache(p.props[0].value)),p.props.forEach((e=>e.key.isHandlerKey=!0)),p},co=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,i=e.arg;return 4!==i.type?(i.children.unshift("("),i.children.push(') || ""')):i.isStatic||(i.content=`${i.content} || ""`),r.includes("camel")&&(4===i.type?i.content=i.isStatic?k(i.content):`${n.helperString(re)}(${i.content})`:(i.children.unshift(`${n.helperString(re)}(`),i.children.push(")"))),n.inSSR||(r.includes("prop")&&lo(i,"."),r.includes("attr")&&lo(i,"^")),!o||4===o.type&&!o.content.trim()?{props:[ke(i,Ne("",!0,s))]}:{props:[ke(i,o)]}},lo=(e,t)=>{4===e.type?e.content=e.isStatic?t+e.content:`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},ao=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(tt(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!tt(s)){o=void 0;break}o||(o=n[e]=Te([t],t.loc)),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const o=n[e];if(tt(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==qt(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:Ee(t.helper(D),r)}}}}},po=new WeakSet,uo=(e,t)=>{if(1===e.type&&Ye(e,"once",!0)){if(po.has(e)||t.inVOnce)return;return po.add(e),t.inVOnce=!0,t.helper(ce),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},fo=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return ho();const s=o.loc.source,i=4===o.type?o.content:s,c=n.bindingMetadata[s];if("props"===c||"props-aliased"===c)return ho();if(!i.trim()||!ze(i))return ho();const l=r||Ne("modelValue",!0),a=r?Le(r)?`onUpdate:${k(r.content)}`:Te(['"onUpdate:" + ',r]):"onUpdate:modelValue";let p;p=Te([`${n.isTS?"($event: any)":"$event"} => ((`,o,") = $event)"]);const u=[ke(l,e.exp),ke(a,p)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(Fe(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?Le(r)?`${r.content}Modifiers`:Te([r,' + "Modifiers"']):"modelModifiers";u.push(ke(n,Ne(`{ ${t} }`,!1,e.loc,2)))}return ho(u)};function ho(e=[]){return{props:e}}const mo=/[\w).+\-_$\]]/,go=(e,t)=>{yt("COMPILER_FILTER",t)&&(5===e.type&&yo(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&yo(e.exp,t)})))};function yo(e,t){if(4===e.type)vo(e,t);else for(let n=0;n<e.children.length;n++){const o=e.children[n];"object"==typeof o&&(4===o.type?vo(o,t):8===o.type?yo(e,t):5===o.type&&yo(o.content,t))}}function vo(e,t){const n=e.content;let o,r,s,i,c=!1,l=!1,a=!1,p=!1,u=0,f=0,d=0,h=0,m=[];for(s=0;s<n.length;s++)if(r=o,o=n.charCodeAt(s),c)39===o&&92!==r&&(c=!1);else if(l)34===o&&92!==r&&(l=!1);else if(a)96===o&&92!==r&&(a=!1);else if(p)47===o&&92!==r&&(p=!1);else if(124!==o||124===n.charCodeAt(s+1)||124===n.charCodeAt(s-1)||u||f||d){switch(o){case 34:l=!0;break;case 39:c=!0;break;case 96:a=!0;break;case 40:d++;break;case 41:d--;break;case 91:f++;break;case 93:f--;break;case 123:u++;break;case 125:u--}if(47===o){let e,t=s-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&mo.test(e)||(p=!0)}}else void 0===i?(h=s+1,i=n.slice(0,s).trim()):g();function g(){m.push(n.slice(h,s).trim()),h=s+1}if(void 0===i?i=n.slice(0,s).trim():0!==h&&g(),m.length){for(s=0;s<m.length;s++)i=bo(i,m[s],t);e.content=i}}function bo(e,t,n){n.helper(z);const o=t.indexOf("(");if(o<0)return n.filters.add(t),`${ut(t,"filter")}(${e})`;{const r=t.slice(0,o),s=t.slice(o+1);return n.filters.add(r),`${ut(r,"filter")}(${e}${")"!==s?","+s:s}`}}const So=new WeakSet,xo=(e,t)=>{if(1===e.type){const n=Ye(e,"memo");if(!n||So.has(e))return;return So.add(e),()=>{const o=e.codegenNode||t.currentNode.codegenNode;o&&13===o.type&&(1!==e.tagType&&ht(o,t),e.codegenNode=Ee(t.helper(de),[n.exp,we(void 0,o),"_cache",String(t.cached++)]))}}};function ko(e){return[[uo,On,xo,Pn,go,oo,Zn,Hn,ao],{on:io,bind:co,model:fo}]}function No(e,t={}){const n=t.onError||w,o="module"===t.mode;!0===t.prefixIdentifiers?n($(47)):o&&n($(48));t.cacheHandlers&&n($(49)),t.scopeId&&!o&&n($(50));const r=m(e)?Nt(e,t):e,[s,i]=ko();return nn(r,d({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:d({},i,t.directiveTransforms||{})})),ln(r,d({},t,{prefixIdentifiers:false}))}const _o=()=>({props:[]}),To=Symbol(""),Eo=Symbol(""),wo=Symbol(""),Oo=Symbol(""),$o=Symbol(""),Co=Symbol(""),Mo=Symbol(""),Io=Symbol(""),Po=Symbol(""),Ro=Symbol("");let Vo;ge({[To]:"vModelRadio",[Eo]:"vModelCheckbox",[wo]:"vModelText",[Oo]:"vModelSelect",[$o]:"vModelDynamic",[Co]:"withModifiers",[Mo]:"withKeys",[Io]:"vShow",[Po]:"Transition",[Ro]:"TransitionGroup"});const Lo=e("style,iframe,script,noscript",!0),Ao={isVoidTag:c,isNativeTag:e=>s(e)||i(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return Vo||(Vo=document.createElement("div")),t?(Vo.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,Vo.children[0].getAttribute("foo")):(Vo.innerHTML=e,Vo.textContent)},isBuiltInComponent:e=>Ae(e,"Transition")?Po:Ae(e,"TransitionGroup")?Ro:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(Lo(e))return 2}return 0}},Bo=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:Ne("style",!0,t.loc),exp:jo(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},jo=(e,t)=>{const s=function(e){const t={};return e.replace(r,"").split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return Ne(JSON.stringify(s),!1,t,3)};function Fo(e,t){return $(e,t)}const Do=e("passive,once,capture"),Ho=e("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),Wo=e("left,right"),Uo=e("onkeyup,onkeydown,onkeypress",!0),Jo=(e,t)=>Le(e)&&"onclick"===e.content.toLowerCase()?Ne(t,!0):4!==e.type?Te(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,zo=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Go=[Bo],Ko={cloak:_o,html:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[ke(Ne("innerHTML",!0,r),o||Ne("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[ke(Ne("textContent",!0),o?qt(o,n)>0?o:Ee(n.helperString(Y),[o],r):Ne("",!0))]}},model:(e,t,n)=>{const o=fo(e,t,n);if(!o.props.length||1===t.tagType)return o;const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let e=wo,i=!1;if("input"===r||s){const n=Qe(t,"type");if(n){if(7===n.type)e=$o;else if(n.value)switch(n.value.content){case"radio":e=To;break;case"checkbox":e=Eo;break;case"file":i=!0}}else et(t)&&(e=$o)}else"select"===r&&(e=Oo);i||(o.needRuntime=n.helper(e))}return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>io(e,t,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:i,nonKeyModifiers:c,eventOptionModifiers:l}=((e,t,n,o)=>{const r=[],s=[],i=[];for(let c=0;c<t.length;c++){const o=t[c];"native"===o&&vt("COMPILER_V_ON_NATIVE",n)||Do(o)?i.push(o):Wo(o)?Le(e)?Uo(e.content)?r.push(o):s.push(o):(r.push(o),s.push(o)):Ho(o)?s.push(o):r.push(o)}return{keyModifiers:r,nonKeyModifiers:s,eventOptionModifiers:i}})(r,o,n);if(c.includes("right")&&(r=Jo(r,"onContextmenu")),c.includes("middle")&&(r=Jo(r,"onMouseup")),c.length&&(s=Ee(n.helper(Co),[s,JSON.stringify(c)])),!i.length||Le(r)&&!Uo(r.content)||(s=Ee(n.helper(Mo),[s,JSON.stringify(i)])),l.length){const e=l.map(T).join("");r=Le(r)?Ne(`${r.content}${e}`,!0):Te(["(",r,`) + "${e}"`])}return{props:[ke(r,s)]}})),show:(e,t,n)=>({props:[],needRuntime:n.helper(Io)})};function qo(e,t={}){return No(e,d({},Ao,t,{nodeTransforms:[zo,...Go,...t.nodeTransforms||[]],directiveTransforms:d({},Ko,t.directiveTransforms||{}),transformHoist:null}))}function Zo(e,t={}){return Nt(e,d({},Ao,t))}export{R as BASE_TRANSITION,re as CAMELIZE,se as CAPITALIZE,L as CREATE_BLOCK,F as CREATE_COMMENT,A as CREATE_ELEMENT_BLOCK,j as CREATE_ELEMENT_VNODE,Z as CREATE_SLOTS,H as CREATE_STATIC,D as CREATE_TEXT,B as CREATE_VNODE,Ko as DOMDirectiveTransforms,Go as DOMNodeTransforms,C as FRAGMENT,ne as GUARD_REACTIVE_PROPS,he as IS_MEMO_SAME,fe as IS_REF,P as KEEP_ALIVE,Q as MERGE_PROPS,X as NORMALIZE_CLASS,te as NORMALIZE_PROPS,ee as NORMALIZE_STYLE,V as OPEN_BLOCK,ae as POP_SCOPE_ID,le as PUSH_SCOPE_ID,K as RENDER_LIST,q as RENDER_SLOT,W as RESOLVE_COMPONENT,J as RESOLVE_DIRECTIVE,U as RESOLVE_DYNAMIC_COMPONENT,z as RESOLVE_FILTER,ce as SET_BLOCK_TRACKING,I as SUSPENSE,M as TELEPORT,Y as TO_DISPLAY_STRING,oe as TO_HANDLERS,ie as TO_HANDLER_KEY,Po as TRANSITION,Ro as TRANSITION_GROUP,ue as UNREF,Eo as V_MODEL_CHECKBOX,$o as V_MODEL_DYNAMIC,To as V_MODEL_RADIO,Oo as V_MODEL_SELECT,wo as V_MODEL_TEXT,Mo as V_ON_WITH_KEYS,Co as V_ON_WITH_MODIFIERS,Io as V_SHOW,pe as WITH_CTX,G as WITH_DIRECTIVES,de as WITH_MEMO,Ke as advancePositionWithClone,qe as advancePositionWithMutation,Ze as assert,No as baseCompile,Nt as baseParse,to as buildDirectiveArgs,Qn as buildProps,Jn as buildSlots,vt as checkCompatEnabled,qo as compile,Se as createArrayExpression,Pe as createAssignmentExpression,Ce as createBlockStatement,$e as createCacheExpression,Ee as createCallExpression,$ as createCompilerError,Te as createCompoundExpression,Oe as createConditionalExpression,Fo as createDOMCompilerError,Fn as createForLoopParams,we as createFunctionExpression,Ie as createIfStatement,_e as createInterpolation,xe as createObjectExpression,ke as createObjectProperty,Ve as createReturnStatement,ve as createRoot,Re as createSequenceExpression,Ne as createSimpleExpression,rn as createStructuralDirectiveTransform,Me as createTemplateLiteral,tn as createTransformContext,be as createVNodeCall,xn as extractIdentifiers,Ye as findDir,Qe as findProp,ln as generate,t as generateCodeFrame,ko as getBaseTransformPreset,qt as getConstantType,Ge as getInnerRange,dt as getMemoedVNodeCall,it as getVNodeBlockHelper,st as getVNodeHelper,et as hasDynamicKeyVBind,ft as hasScopeRef,me as helperNameMap,at as injectProp,Ae as isBuiltInType,Be as isCoreComponent,kn as isFunctionType,vn as isInDestructureAssignment,ze as isMemberExpression,Ue as isMemberExpressionBrowser,Je as isMemberExpressionNode,yn as isReferencedIdentifier,Fe as isSimpleIdentifier,rt as isSlotOutlet,Xe as isStaticArgOf,Le as isStaticExp,Nn as isStaticProperty,_n as isStaticPropertyKey,ot as isTemplateNode,tt as isText,nt as isVSlot,ye as locStub,ht as makeBlock,_o as noopDirectiveTransform,Zo as parse,Ao as parserOptions,En as processExpression,Rn as processFor,$n as processIf,ro as processSlotOutlet,ge as registerRuntimeHelpers,Yn as resolveComponentType,wn as stringifyExpression,ut as toValidAssetId,Hn as trackSlotScopes,Wn as trackVForSlotScopes,nn as transform,co as transformBind,Zn as transformElement,Tn as transformExpression,fo as transformModel,io as transformOn,Bo as transformStyle,on as traverseNode,Sn as walkBlockDeclarations,bn as walkFunctionParams,gn as walkIdentifiers,bt as warnDeprecation};

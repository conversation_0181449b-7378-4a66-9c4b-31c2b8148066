<template>
  <view class="chess-page">
    <!-- 游戏头部 -->
    <view class="game-header">
      <view class="player-info">
        <text class="player-name">玩家</text>
        <text class="player-status" :class="{ 'active': currentPlayer === 'red' }">
          {{ currentPlayer === 'red' ? '你的回合' : '等待中' }}
        </text>
      </view>
      
      <view class="game-controls">
        <button class="btn btn-small btn-outline" @click="resetGame">重新开始</button>
        <button class="btn btn-small btn-primary" @click="undoMove" :disabled="!canUndo">悔棋</button>
      </view>
      
      <view class="player-info">
        <text class="player-name">AI</text>
        <text class="player-status" :class="{ 'active': currentPlayer === 'black' }">
          {{ currentPlayer === 'black' ? 'AI思考中' : '等待中' }}
        </text>
      </view>
    </view>
    
    <!-- 棋盘 -->
    <view class="chess-board-container">
      <canvas 
        class="chess-board" 
        canvas-id="chessBoard"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
      ></canvas>
    </view>
    
    <!-- 游戏信息 -->
    <view class="game-info">
      <view class="info-item">
        <text class="info-label">当前回合:</text>
        <text class="info-value">{{ currentPlayer === 'red' ? '红方(玩家)' : '黑方(AI)' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">游戏状态:</text>
        <text class="info-value">{{ gameStatusText }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">步数:</text>
        <text class="info-value">{{ moveCount }}</text>
      </view>
    </view>
    
    <!-- 游戏结果弹窗 -->
    <view class="game-result-modal" v-if="gameResult" @click="closeResult">
      <view class="modal-content" @click.stop>
        <view class="result-icon">
          <text>{{ gameResult.winner === 'red' ? '🎉' : '😔' }}</text>
        </view>
        <text class="result-title">{{ gameResult.title }}</text>
        <text class="result-message">{{ gameResult.message }}</text>
        <view class="result-actions">
          <button class="btn btn-primary" @click="resetGame">再来一局</button>
          <button class="btn btn-outline" @click="closeResult">返回</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 游戏状态
      currentPlayer: 'red', // red: 玩家, black: AI
      gameStatus: 'playing', // playing, checkmate, stalemate
      moveCount: 0,
      canUndo: false,
      gameResult: null,
      
      // 棋盘相关
      boardSize: 0,
      cellSize: 0,
      selectedPiece: null,
      
      // 简化的棋盘状态 (9x10)
      board: [
        ['r', 'n', 'b', 'a', 'k', 'a', 'b', 'n', 'r'],
        ['', '', '', '', '', '', '', '', ''],
        ['', 'c', '', '', '', '', '', 'c', ''],
        ['p', '', 'p', '', 'p', '', 'p', '', 'p'],
        ['', '', '', '', '', '', '', '', ''],
        ['', '', '', '', '', '', '', '', ''],
        ['P', '', 'P', '', 'P', '', 'P', '', 'P'],
        ['', 'C', '', '', '', '', '', 'C', ''],
        ['', '', '', '', '', '', '', '', ''],
        ['R', 'N', 'B', 'A', 'K', 'A', 'B', 'N', 'R']
      ],
      
      // 棋子名称映射
      pieceNames: {
        'k': '将', 'K': '帅',
        'a': '士', 'A': '仕',
        'b': '象', 'B': '相',
        'n': '马', 'N': '马',
        'r': '车', 'R': '车',
        'c': '炮', 'C': '炮',
        'p': '卒', 'P': '兵'
      }
    }
  },
  
  computed: {
    gameStatusText() {
      switch (this.gameStatus) {
        case 'playing': return '游戏进行中'
        case 'checkmate': return '将军！'
        case 'stalemate': return '和棋'
        default: return '未知状态'
      }
    }
  },
  
  onLoad() {
    this.initGame()
  },
  
  onReady() {
    this.initCanvas()
  },
  
  methods: {
    // 初始化游戏
    initGame() {
      // 记录游戏开始
      this.recordGameStart()
    },
    
    // 初始化画布
    initCanvas() {
      const query = uni.createSelectorQuery()
      query.select('.chess-board').boundingClientRect((rect) => {
        this.boardSize = Math.min(rect.width, 400)
        this.cellSize = this.boardSize / 9
        this.drawBoard()
      }).exec()
    },
    
    // 绘制棋盘
    drawBoard() {
      const ctx = uni.createCanvasContext('chessBoard', this)
      
      // 清空画布
      ctx.clearRect(0, 0, this.boardSize, this.boardSize * 10 / 9)
      
      // 绘制棋盘线条
      ctx.setStrokeStyle('#8B4513')
      ctx.setLineWidth(2)
      
      // 绘制横线
      for (let i = 0; i <= 9; i++) {
        ctx.beginPath()
        ctx.moveTo(0, i * this.cellSize)
        ctx.lineTo(this.boardSize, i * this.cellSize)
        ctx.stroke()
      }
      
      // 绘制竖线
      for (let i = 0; i <= 8; i++) {
        ctx.beginPath()
        ctx.moveTo(i * this.cellSize, 0)
        ctx.lineTo(i * this.cellSize, this.boardSize * 10 / 9)
        ctx.stroke()
      }
      
      // 绘制楚河汉界
      ctx.setFontSize(16)
      ctx.setFillStyle('#8B4513')
      ctx.fillText('楚河', this.cellSize * 1.5, this.cellSize * 4.7)
      ctx.fillText('汉界', this.cellSize * 5.5, this.cellSize * 4.7)
      
      // 绘制棋子
      this.drawPieces(ctx)
      
      ctx.draw()
    },
    
    // 绘制棋子
    drawPieces(ctx) {
      for (let row = 0; row < 10; row++) {
        for (let col = 0; col < 9; col++) {
          const piece = this.board[row][col]
          if (piece) {
            const x = col * this.cellSize
            const y = row * this.cellSize
            const radius = this.cellSize * 0.4
            
            // 绘制棋子背景
            ctx.setFillStyle(piece === piece.toUpperCase() ? '#FFE4B5' : '#8B4513')
            ctx.beginPath()
            ctx.arc(x + this.cellSize / 2, y + this.cellSize / 2, radius, 0, 2 * Math.PI)
            ctx.fill()
            
            // 绘制棋子边框
            ctx.setStrokeStyle('#000')
            ctx.setLineWidth(2)
            ctx.stroke()
            
            // 绘制棋子文字
            ctx.setFontSize(this.cellSize * 0.5)
            ctx.setFillStyle(piece === piece.toUpperCase() ? '#8B4513' : '#FFE4B5')
            ctx.setTextAlign('center')
            ctx.fillText(
              this.pieceNames[piece] || piece,
              x + this.cellSize / 2,
              y + this.cellSize / 2 + this.cellSize * 0.15
            )
          }
        }
      }
    },
    
    // 处理触摸开始
    handleTouchStart(e) {
      if (this.currentPlayer !== 'red') return
      
      const touch = e.touches[0]
      const col = Math.floor(touch.x / this.cellSize)
      const row = Math.floor(touch.y / this.cellSize)
      
      if (col >= 0 && col < 9 && row >= 0 && row < 10) {
        const piece = this.board[row][col]
        
        if (piece && piece === piece.toUpperCase()) {
          // 选中己方棋子
          this.selectedPiece = { row, col, piece }
        } else if (this.selectedPiece) {
          // 移动棋子
          this.movePiece(this.selectedPiece.row, this.selectedPiece.col, row, col)
          this.selectedPiece = null
        }
      }
    },
    
    // 处理触摸结束
    handleTouchEnd(e) {
      // 可以在这里添加触摸结束的逻辑
    },
    
    // 移动棋子
    movePiece(fromRow, fromCol, toRow, toCol) {
      // 简单的移动逻辑（实际象棋需要复杂的规则验证）
      if (this.isValidMove(fromRow, fromCol, toRow, toCol)) {
        // 执行移动
        const piece = this.board[fromRow][fromCol]
        this.board[toRow][toCol] = piece
        this.board[fromRow][fromCol] = ''
        
        this.moveCount++
        this.canUndo = true
        
        // 重绘棋盘
        this.drawBoard()
        
        // 切换到AI回合
        this.currentPlayer = 'black'
        
        // AI自动下棋
        setTimeout(() => {
          this.aiMove()
        }, 1000)
      }
    },
    
    // 简单的移动验证
    isValidMove(fromRow, fromCol, toRow, toCol) {
      // 这里应该实现完整的象棋规则验证
      // 为了简化，这里只做基本检查
      
      if (toRow < 0 || toRow >= 10 || toCol < 0 || toCol >= 9) {
        return false
      }
      
      const targetPiece = this.board[toRow][toCol]
      if (targetPiece && targetPiece === targetPiece.toUpperCase()) {
        // 不能吃己方棋子
        return false
      }
      
      return true
    },
    
    // AI移动
    aiMove() {
      // 简单的AI逻辑：随机选择一个可移动的棋子
      const blackPieces = []
      
      for (let row = 0; row < 10; row++) {
        for (let col = 0; col < 9; col++) {
          const piece = this.board[row][col]
          if (piece && piece === piece.toLowerCase()) {
            blackPieces.push({ row, col, piece })
          }
        }
      }
      
      if (blackPieces.length > 0) {
        const randomPiece = blackPieces[Math.floor(Math.random() * blackPieces.length)]
        
        // 随机选择一个目标位置
        const possibleMoves = []
        for (let row = 0; row < 10; row++) {
          for (let col = 0; col < 9; col++) {
            if (this.isValidAIMove(randomPiece.row, randomPiece.col, row, col)) {
              possibleMoves.push({ row, col })
            }
          }
        }
        
        if (possibleMoves.length > 0) {
          const randomMove = possibleMoves[Math.floor(Math.random() * possibleMoves.length)]
          
          // 执行AI移动
          this.board[randomMove.row][randomMove.col] = randomPiece.piece
          this.board[randomPiece.row][randomPiece.col] = ''
          
          this.moveCount++
          this.drawBoard()
          
          // 切换回玩家回合
          this.currentPlayer = 'red'
        }
      }
    },
    
    // AI移动验证
    isValidAIMove(fromRow, fromCol, toRow, toCol) {
      if (toRow < 0 || toRow >= 10 || toCol < 0 || toCol >= 9) {
        return false
      }
      
      const targetPiece = this.board[toRow][toCol]
      if (targetPiece && targetPiece === targetPiece.toLowerCase()) {
        return false
      }
      
      // 简单的移动距离限制
      const rowDiff = Math.abs(toRow - fromRow)
      const colDiff = Math.abs(toCol - fromCol)
      
      return rowDiff <= 2 && colDiff <= 2 && (rowDiff > 0 || colDiff > 0)
    },
    
    // 悔棋
    undoMove() {
      uni.showToast({
        title: '悔棋功能开发中',
        icon: 'none'
      })
    },
    
    // 重新开始
    resetGame() {
      this.currentPlayer = 'red'
      this.gameStatus = 'playing'
      this.moveCount = 0
      this.canUndo = false
      this.gameResult = null
      this.selectedPiece = null
      
      // 重置棋盘
      this.board = [
        ['r', 'n', 'b', 'a', 'k', 'a', 'b', 'n', 'r'],
        ['', '', '', '', '', '', '', '', ''],
        ['', 'c', '', '', '', '', '', 'c', ''],
        ['p', '', 'p', '', 'p', '', 'p', '', 'p'],
        ['', '', '', '', '', '', '', '', ''],
        ['', '', '', '', '', '', '', '', ''],
        ['P', '', 'P', '', 'P', '', 'P', '', 'P'],
        ['', 'C', '', '', '', '', '', 'C', ''],
        ['', '', '', '', '', '', '', '', ''],
        ['R', 'N', 'B', 'A', 'K', 'A', 'B', 'N', 'R']
      ]
      
      this.drawBoard()
    },
    
    // 关闭结果弹窗
    closeResult() {
      this.gameResult = null
    },
    
    // 记录游戏开始
    recordGameStart() {
      // 触发游戏任务
      this.$store.dispatch('redpacket/checkUserBehavior', 'play_game')
    }
  }
}
</script>

<style lang="scss" scoped>
.chess-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 32rpx;
}

/* 游戏头部 */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.player-info {
  text-align: center;
}

.player-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.player-status {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
}

.player-status.active {
  color: #dc2626;
  font-weight: bold;
}

.game-controls {
  display: flex;
  gap: 16rpx;
}

/* 棋盘容器 */
.chess-board-container {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.chess-board {
  width: 100%;
  max-width: 400px;
  height: 444px; /* 400 * 10/9 */
  background: #F5DEB3;
  border: 2px solid #8B4513;
  border-radius: 8px;
}

/* 游戏信息 */
.game-info {
  background: white;
  border-radius: 12px;
  padding: 24rpx;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #6b7280;
}

.info-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 游戏结果弹窗 */
.game-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 64rpx 48rpx;
  text-align: center;
  margin: 32rpx;
  max-width: 500rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 16rpx;
}

.result-message {
  font-size: 28rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 48rpx;
}

.result-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}
</style>

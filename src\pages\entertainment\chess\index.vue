<template>
  <view class="chess-page">
    <!-- 游戏头部 -->
    <view class="game-header">
      <view class="player-info">
        <text class="player-name">玩家</text>
        <text class="player-status" :class="{ active: currentPlayer === 'red' }">红方</text>
      </view>
      <view class="game-status">
        <text class="status-text">{{ gameStatusText }}</text>
        <text class="move-count">第{{ moveCount }}步</text>
      </view>
      <view class="player-info">
        <text class="player-name">AI</text>
        <text class="player-status" :class="{ active: currentPlayer === 'black' }">黑方</text>
      </view>
    </view>
    
    <!-- 棋盘容器 -->
    <view class="chess-board-container">
      <canvas 
        class="chess-board" 
        canvas-id="chessBoard"
        @touchstart="onTouchStart"
        @touchend="onTouchEnd"
      ></canvas>
      
      <!-- 游戏控制按钮 -->
      <view class="game-controls">
        <button class="control-btn" @click="undoMove" :disabled="!canUndo">
          <text class="btn-icon">↶</text>
          <text class="btn-text">悔棋</text>
        </button>
        <button class="control-btn" @click="restartGame">
          <text class="btn-icon">🔄</text>
          <text class="btn-text">重新开始</text>
        </button>
        <button class="control-btn" @click="showSettings">
          <text class="btn-icon">⚙️</text>
          <text class="btn-text">设置</text>
        </button>
      </view>
    </view>
    
    <!-- 游戏结果弹窗 -->
    <view class="game-result-modal" v-if="showResult">
      <view class="modal-content">
        <view class="result-icon">{{ resultIcon }}</view>
        <text class="result-title">{{ resultTitle }}</text>
        <text class="result-desc">{{ resultDesc }}</text>
        <view class="result-stats">
          <text class="stat-item">用时: {{ formatTime(gameTime) }}</text>
          <text class="stat-item">步数: {{ moveCount }}</text>
        </view>
        <view class="result-actions">
          <button class="btn btn-primary" @click="playAgain">再来一局</button>
          <button class="btn btn-outline" @click="closeResult">返回</button>
        </view>
      </view>
    </view>
    
    <!-- 设置弹窗 -->
    <view class="settings-modal" v-if="showSettingsModal">
      <view class="modal-content">
        <view class="settings-header">
          <text class="settings-title">游戏设置</text>
          <text class="close-btn" @click="closeSettings">✕</text>
        </view>
        <view class="settings-list">
          <view class="setting-item">
            <text class="setting-label">音效</text>
            <switch :checked="soundEnabled" @change="toggleSound" />
          </view>
          <view class="setting-item">
            <text class="setting-label">AI难度</text>
            <picker :value="aiLevel" :range="aiLevels" @change="changeAiLevel">
              <view class="picker-text">{{ aiLevels[aiLevel] }}</view>
            </picker>
          </view>
          <view class="setting-item">
            <text class="setting-label">棋盘主题</text>
            <picker :value="boardTheme" :range="boardThemes" @change="changeBoardTheme">
              <view class="picker-text">{{ boardThemes[boardTheme] }}</view>
            </picker>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'

export default {
  data() {
    return {
      // 游戏状态
      gameStatus: 'playing', // playing, win, lose, draw
      currentPlayer: 'red', // red, black
      moveCount: 0,
      gameTime: 0,
      gameTimer: null,
      
      // 棋盘数据
      boardSize: 9,
      cellSize: 0,
      selectedPiece: null,
      possibleMoves: [],
      
      // 弹窗状态
      showResult: false,
      showSettingsModal: false,
      
      // 设置
      soundEnabled: true,
      aiLevel: 1,
      boardTheme: 0,
      
      // 配置选项
      aiLevels: ['简单', '中等', '困难'],
      boardThemes: ['经典', '现代', '复古'],
      
      // 游戏历史
      moveHistory: [],
      
      // Canvas上下文
      ctx: null
    }
  },
  
  computed: {
    redPacketStore() {
      return useRedPacketStore()
    },
    
    gameStatusText() {
      switch (this.gameStatus) {
        case 'playing':
          return this.currentPlayer === 'red' ? '轮到您走棋' : 'AI思考中...'
        case 'win':
          return '恭喜获胜！'
        case 'lose':
          return '很遗憾失败了'
        case 'draw':
          return '平局'
        default:
          return '游戏进行中'
      }
    },
    
    canUndo() {
      return this.moveHistory.length > 0 && this.gameStatus === 'playing'
    },
    
    resultIcon() {
      switch (this.gameStatus) {
        case 'win': return '🎉'
        case 'lose': return '😔'
        case 'draw': return '🤝'
        default: return '🎮'
      }
    },
    
    resultTitle() {
      switch (this.gameStatus) {
        case 'win': return '恭喜获胜！'
        case 'lose': return '再接再厉！'
        case 'draw': return '平局'
        default: return '游戏结束'
      }
    },
    
    resultDesc() {
      switch (this.gameStatus) {
        case 'win': return '您的棋艺很不错！'
        case 'lose': return '多练习就能提高棋艺'
        case 'draw': return '势均力敌的对局'
        default: return ''
      }
    }
  },
  
  onLoad() {
    this.initGame()
  },
  
  onReady() {
    this.initCanvas()
  },
  
  onUnload() {
    this.clearTimer()
  },
  
  methods: {
    // 初始化游戏
    initGame() {
      this.gameStatus = 'playing'
      this.currentPlayer = 'red'
      this.moveCount = 0
      this.gameTime = 0
      this.moveHistory = []
      this.selectedPiece = null
      this.possibleMoves = []
      
      this.startTimer()
      this.loadSettings()
      
      // 记录游戏开始
      this.recordGameStart()
    },
    
    // 初始化Canvas
    initCanvas() {
      const query = uni.createSelectorQuery()
      query.select('.chess-board').boundingClientRect((rect) => {
        if (rect) {
          this.cellSize = Math.min(rect.width, rect.height) / 10
          this.ctx = uni.createCanvasContext('chessBoard', this)
          this.drawBoard()
          this.drawPieces()
        }
      }).exec()
    },
    
    // 绘制棋盘
    drawBoard() {
      if (!this.ctx) return
      
      const ctx = this.ctx
      const size = this.cellSize
      
      // 清空画布
      ctx.clearRect(0, 0, size * 10, size * 11)
      
      // 绘制背景
      ctx.setFillStyle('#f4e4bc')
      ctx.fillRect(0, 0, size * 10, size * 11)
      
      // 绘制网格线
      ctx.setStrokeStyle('#8b4513')
      ctx.setLineWidth(2)
      
      // 横线
      for (let i = 0; i <= 9; i++) {
        ctx.beginPath()
        ctx.moveTo(size, size * (i + 1))
        ctx.lineTo(size * 9, size * (i + 1))
        ctx.stroke()
      }
      
      // 竖线
      for (let i = 0; i <= 8; i++) {
        ctx.beginPath()
        ctx.moveTo(size * (i + 1), size)
        ctx.lineTo(size * (i + 1), size * 5)
        ctx.stroke()
        
        ctx.beginPath()
        ctx.moveTo(size * (i + 1), size * 6)
        ctx.lineTo(size * (i + 1), size * 10)
        ctx.stroke()
      }
      
      // 绘制楚河汉界
      ctx.setFillStyle('#8b4513')
      ctx.setFontSize(24)
      ctx.setTextAlign('center')
      ctx.fillText('楚河', size * 3, size * 5.7)
      ctx.fillText('汉界', size * 7, size * 5.7)
      
      ctx.draw()
    },
    
    // 绘制棋子
    drawPieces() {
      if (!this.ctx) return
      
      // 这里应该根据实际的棋盘状态绘制棋子
      // 为了简化，我们只绘制一些示例棋子
      const ctx = this.ctx
      const size = this.cellSize
      
      // 绘制红方棋子示例
      this.drawPiece(ctx, 1, 0, '车', 'red')
      this.drawPiece(ctx, 2, 0, '马', 'red')
      this.drawPiece(ctx, 3, 0, '相', 'red')
      this.drawPiece(ctx, 4, 0, '仕', 'red')
      this.drawPiece(ctx, 5, 0, '帅', 'red')
      
      // 绘制黑方棋子示例
      this.drawPiece(ctx, 1, 9, '车', 'black')
      this.drawPiece(ctx, 2, 9, '马', 'black')
      this.drawPiece(ctx, 3, 9, '象', 'black')
      this.drawPiece(ctx, 4, 9, '士', 'black')
      this.drawPiece(ctx, 5, 9, '将', 'black')
      
      ctx.draw(true)
    },
    
    // 绘制单个棋子
    drawPiece(ctx, x, y, text, color) {
      const size = this.cellSize
      const centerX = size * (x + 1)
      const centerY = size * (y + 1)
      const radius = size * 0.4
      
      // 绘制棋子背景
      ctx.setFillStyle(color === 'red' ? '#ff6b6b' : '#2c3e50')
      ctx.beginPath()
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
      ctx.fill()
      
      // 绘制棋子边框
      ctx.setStrokeStyle('#333')
      ctx.setLineWidth(2)
      ctx.stroke()
      
      // 绘制棋子文字
      ctx.setFillStyle('white')
      ctx.setFontSize(size * 0.3)
      ctx.setTextAlign('center')
      ctx.fillText(text, centerX, centerY + size * 0.1)
    },
    
    // 触摸开始
    onTouchStart(e) {
      if (this.gameStatus !== 'playing' || this.currentPlayer !== 'red') return
      
      const touch = e.touches[0]
      const x = Math.round((touch.x - this.cellSize) / this.cellSize)
      const y = Math.round((touch.y - this.cellSize) / this.cellSize)
      
      this.handlePieceClick(x, y)
    },
    
    // 触摸结束
    onTouchEnd(e) {
      // 处理触摸结束逻辑
    },
    
    // 处理棋子点击
    handlePieceClick(x, y) {
      // 简化的点击处理逻辑
      if (this.selectedPiece) {
        // 尝试移动棋子
        this.movePiece(this.selectedPiece, { x, y })
        this.selectedPiece = null
      } else {
        // 选择棋子
        this.selectedPiece = { x, y }
      }
    },
    
    // 移动棋子
    movePiece(from, to) {
      // 简化的移动逻辑
      this.moveCount++
      this.moveHistory.push({ from, to, timestamp: Date.now() })
      
      // 播放移动音效
      if (this.soundEnabled) {
        this.playMoveSound()
      }
      
      // 切换玩家
      this.currentPlayer = this.currentPlayer === 'red' ? 'black' : 'red'
      
      // 重新绘制棋盘
      this.drawBoard()
      this.drawPieces()
      
      // AI回合
      if (this.currentPlayer === 'black') {
        setTimeout(() => {
          this.aiMove()
        }, 1000)
      }
    },
    
    // AI移动
    aiMove() {
      // 简化的AI逻辑
      this.moveCount++
      this.currentPlayer = 'red'
      
      // 随机决定游戏结果（演示用）
      if (this.moveCount > 10 && Math.random() < 0.3) {
        this.endGame(Math.random() < 0.5 ? 'win' : 'lose')
      }
    },
    
    // 悔棋
    undoMove() {
      if (!this.canUndo) return
      
      // 撤销最近两步（玩家和AI）
      this.moveHistory.pop()
      if (this.moveHistory.length > 0) {
        this.moveHistory.pop()
      }
      
      this.moveCount = Math.max(0, this.moveCount - 2)
      this.currentPlayer = 'red'
      
      // 重新绘制棋盘
      this.drawBoard()
      this.drawPieces()
      
      uni.showToast({
        title: '已悔棋',
        icon: 'success'
      })
    },
    
    // 重新开始游戏
    restartGame() {
      uni.showModal({
        title: '确认重新开始',
        content: '当前游戏进度将丢失，确定要重新开始吗？',
        success: (res) => {
          if (res.confirm) {
            this.initGame()
            this.drawBoard()
            this.drawPieces()
          }
        }
      })
    },
    
    // 结束游戏
    endGame(result) {
      this.gameStatus = result
      this.clearTimer()
      this.showResult = true
      
      // 记录游戏结果
      this.recordGameResult(result)
      
      // 如果获胜，可能触发红包
      if (result === 'win' && Math.random() < 0.3) {
        setTimeout(() => {
          this.showWinRedPacket()
        }, 2000)
      }
    },
    
    // 显示获胜红包
    showWinRedPacket() {
      uni.$emit('show_redpacket', {
        type: 'special',
        amount: Math.floor(Math.random() * 10) + 5,
        title: '象棋获胜红包',
        message: '恭喜您在象棋对弈中获胜！',
        animation: 'bounce'
      })
    },
    
    // 再来一局
    playAgain() {
      this.showResult = false
      this.initGame()
      this.drawBoard()
      this.drawPieces()
    },
    
    // 关闭结果弹窗
    closeResult() {
      this.showResult = false
      uni.navigateBack()
    },
    
    // 显示设置
    showSettings() {
      this.showSettingsModal = true
    },
    
    // 关闭设置
    closeSettings() {
      this.showSettingsModal = false
      this.saveSettings()
    },
    
    // 切换音效
    toggleSound(e) {
      this.soundEnabled = e.detail.value
    },
    
    // 改变AI难度
    changeAiLevel(e) {
      this.aiLevel = e.detail.value
    },
    
    // 改变棋盘主题
    changeBoardTheme(e) {
      this.boardTheme = e.detail.value
      this.drawBoard()
      this.drawPieces()
    },
    
    // 播放移动音效
    playMoveSound() {
      const audio = uni.createInnerAudioContext()
      audio.src = '/static/audio/chess_move.mp3'
      audio.play()
    },
    
    // 开始计时
    startTimer() {
      this.gameTimer = setInterval(() => {
        this.gameTime++
      }, 1000)
    },
    
    // 清除计时器
    clearTimer() {
      if (this.gameTimer) {
        clearInterval(this.gameTimer)
        this.gameTimer = null
      }
    },
    
    // 格式化时间
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },
    
    // 加载设置
    loadSettings() {
      const settings = uni.getStorageSync('chess_settings') || {}
      this.soundEnabled = settings.soundEnabled !== false
      this.aiLevel = settings.aiLevel || 1
      this.boardTheme = settings.boardTheme || 0
    },
    
    // 保存设置
    saveSettings() {
      const settings = {
        soundEnabled: this.soundEnabled,
        aiLevel: this.aiLevel,
        boardTheme: this.boardTheme
      }
      uni.setStorageSync('chess_settings', settings)
    },
    
    // 记录游戏开始
    recordGameStart() {
      const stats = uni.getStorageSync('game_stats') || {}
      stats.totalGames = (stats.totalGames || 0) + 1
      uni.setStorageSync('game_stats', stats)
      
      // 检查首次游戏
      if (stats.totalGames === 1) {
        this.redPacketStore.checkUserBehavior('play_first_game')
      }
    },
    
    // 记录游戏结果
    recordGameResult(result) {
      const stats = uni.getStorageSync('game_stats') || {}
      if (result === 'win') {
        stats.wins = (stats.wins || 0) + 1
      }
      stats.winRate = Math.round((stats.wins || 0) / (stats.totalGames || 1) * 100)
      stats.playTime = (stats.playTime || 0) + Math.floor(this.gameTime / 60)
      uni.setStorageSync('game_stats', stats)
    }
  }
}
</script>

<style lang="scss" scoped>
.chess-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #8B4513, #D2691E);
  padding: 32rpx;
  display: flex;
  flex-direction: column;
}

/* 游戏头部 */
.game-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius);
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.player-info {
  text-align: center;
}

.player-name {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 8rpx;
}

.player-status {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: var(--bg-color);
}

.player-status.active {
  background: var(--primary-color);
  color: white;
}

.game-status {
  text-align: center;
}

.status-text {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.move-count {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}

/* 棋盘容器 */
.chess-board-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chess-board {
  width: 100%;
  max-width: 600rpx;
  height: 660rpx;
  background: #f4e4bc;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 32rpx;
}

/* 游戏控制 */
.game-controls {
  display: flex;
  gap: 24rpx;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: white;
  transform: translateY(-4rpx);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
  color: var(--text-color);
}

/* 游戏结果弹窗 */
.game-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin: 32rpx;
  text-align: center;
  max-width: 500rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.result-desc {
  font-size: 28rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 32rpx;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 24rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
}

.stat-item {
  font-size: 26rpx;
  color: var(--text-color);
}

.result-actions {
  display: flex;
  gap: 24rpx;
}

.result-actions .btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid var(--border-color);
}

.settings-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.close-btn {
  font-size: 32rpx;
  color: var(--text-light);
  cursor: pointer;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 30rpx;
  color: var(--text-color);
}

.picker-text {
  font-size: 28rpx;
  color: var(--primary-color);
  padding: 8rpx 16rpx;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8rpx;
}
</style>

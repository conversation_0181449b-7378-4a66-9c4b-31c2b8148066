<template>
  <view class="entertainment-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-entertainment">
      <text class="header-title">娱乐游戏</text>
      <text class="header-subtitle">放松身心，享受游戏乐趣</text>
    </view>
    
    <!-- 游戏网格 -->
    <view class="games-container">
      <view class="games-grid grid-2">
        <!-- 中国象棋 -->
        <view class="game-card" @click="goToGame('chess')">
          <view class="game-cover">
            <view class="game-icon chess-icon">♟️</view>
            <view class="game-badge hot">热门</view>
          </view>
          <view class="game-info">
            <text class="game-title">中国象棋</text>
            <text class="game-desc">经典象棋对弈，智慧博弈</text>
            <view class="game-stats">
              <text class="stat-item">🎯 AI对战</text>
              <text class="stat-item">⚡ 悔棋功能</text>
            </view>
          </view>
        </view>
        
        <!-- 五子棋 -->
        <view class="game-card" @click="goToGame('gobang')">
          <view class="game-cover">
            <view class="game-icon gobang-icon">⚫</view>
            <view class="game-badge new">新品</view>
          </view>
          <view class="game-info">
            <text class="game-title">五子棋</text>
            <text class="game-desc">五子连珠，策略对弈</text>
            <view class="game-stats">
              <text class="stat-item">🤖 智能AI</text>
              <text class="stat-item">🔄 撤销操作</text>
            </view>
          </view>
        </view>
        
        <!-- 围棋 -->
        <view class="game-card" @click="goToGame('go')">
          <view class="game-cover">
            <view class="game-icon go-icon">⚪</view>
            <view class="game-badge coming">即将上线</view>
          </view>
          <view class="game-info">
            <text class="game-title">围棋</text>
            <text class="game-desc">千年国粹，博大精深</text>
            <view class="game-stats">
              <text class="stat-item">🎨 精美界面</text>
              <text class="stat-item">📚 教学模式</text>
            </view>
          </view>
        </view>
        
        <!-- 消消乐 -->
        <view class="game-card" @click="goToGame('match3')">
          <view class="game-cover">
            <view class="game-icon match3-icon">💎</view>
            <view class="game-badge">休闲</view>
          </view>
          <view class="game-info">
            <text class="game-title">消消乐</text>
            <text class="game-desc">轻松休闲，消除烦恼</text>
            <view class="game-stats">
              <text class="stat-item">🌟 特效华丽</text>
              <text class="stat-item">🎵 音效丰富</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 游戏统计 -->
    <view class="game-stats-card card">
      <view class="stats-header">
        <text class="stats-title">游戏统计</text>
        <text class="stats-period">本月数据</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ gameStats.totalGames }}</text>
          <text class="stat-label">总游戏局数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ gameStats.winRate }}%</text>
          <text class="stat-label">胜率</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ gameStats.playTime }}</text>
          <text class="stat-label">游戏时长(分钟)</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ gameStats.redPackets }}</text>
          <text class="stat-label">获得红包</text>
        </view>
      </view>
    </view>
    
    <!-- 成就系统 -->
    <view class="achievements-card card">
      <view class="achievements-header">
        <text class="achievements-title">游戏成就</text>
        <text class="achievements-count">{{ unlockedAchievements }}/{{ totalAchievements }}</text>
      </view>
      <view class="achievements-list">
        <view 
          class="achievement-item" 
          v-for="achievement in achievements" 
          :key="achievement.id"
          :class="{ unlocked: achievement.unlocked }"
        >
          <view class="achievement-icon">{{ achievement.icon }}</view>
          <view class="achievement-info">
            <text class="achievement-name">{{ achievement.name }}</text>
            <text class="achievement-desc">{{ achievement.description }}</text>
          </view>
          <view class="achievement-reward" v-if="achievement.unlocked">
            <text class="reward-text">+{{ achievement.reward }}分</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 每日挑战 -->
    <view class="daily-challenge card">
      <view class="challenge-header">
        <text class="challenge-title">每日挑战</text>
        <text class="challenge-refresh">{{ refreshTime }}</text>
      </view>
      <view class="challenge-list">
        <view 
          class="challenge-item" 
          v-for="challenge in dailyChallenges" 
          :key="challenge.id"
          :class="{ completed: challenge.completed }"
        >
          <view class="challenge-info">
            <text class="challenge-name">{{ challenge.name }}</text>
            <text class="challenge-progress">{{ challenge.progress }}/{{ challenge.target }}</text>
          </view>
          <view class="challenge-reward">
            <text class="reward-icon">🧧</text>
            <text class="reward-amount">{{ challenge.reward }}分</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'

export default {
  data() {
    return {
      gameStats: {
        totalGames: 0,
        winRate: 0,
        playTime: 0,
        redPackets: 0
      },
      
      achievements: [
        {
          id: 'first_win',
          name: '初战告捷',
          description: '赢得第一局游戏',
          icon: '🏆',
          reward: 5,
          unlocked: false
        },
        {
          id: 'chess_master',
          name: '象棋大师',
          description: '象棋连胜5局',
          icon: '♟️',
          reward: 10,
          unlocked: false
        },
        {
          id: 'gobang_expert',
          name: '五子棋专家',
          description: '五子棋胜率达到70%',
          icon: '⚫',
          reward: 8,
          unlocked: false
        },
        {
          id: 'game_addict',
          name: '游戏达人',
          description: '累计游戏时长超过1小时',
          icon: '🎮',
          reward: 15,
          unlocked: false
        }
      ],
      
      dailyChallenges: [
        {
          id: 'daily_game',
          name: '每日对弈',
          progress: 0,
          target: 3,
          reward: 2,
          completed: false
        },
        {
          id: 'win_streak',
          name: '连胜挑战',
          progress: 0,
          target: 2,
          reward: 5,
          completed: false
        }
      ],
      
      refreshTime: '23:59:59'
    }
  },
  
  computed: {
    redPacketStore() {
      return useRedPacketStore()
    },
    
    unlockedAchievements() {
      return this.achievements.filter(a => a.unlocked).length
    },
    
    totalAchievements() {
      return this.achievements.length
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.updateStats()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.loadGameStats()
      this.loadAchievements()
      this.loadDailyChallenges()
      this.updateRefreshTime()
    },
    
    // 加载游戏统计
    loadGameStats() {
      const stats = uni.getStorageSync('game_stats') || {}
      this.gameStats = {
        totalGames: stats.totalGames || 0,
        winRate: stats.winRate || 0,
        playTime: stats.playTime || 0,
        redPackets: stats.redPackets || 0
      }
    },
    
    // 加载成就数据
    loadAchievements() {
      const savedAchievements = uni.getStorageSync('achievements') || []
      if (savedAchievements.length > 0) {
        this.achievements = savedAchievements
      }
    },
    
    // 加载每日挑战
    loadDailyChallenges() {
      const today = new Date().toDateString()
      const savedChallenges = uni.getStorageSync('daily_challenges')
      
      if (savedChallenges && savedChallenges.date === today) {
        this.dailyChallenges = savedChallenges.challenges
      } else {
        // 重置每日挑战
        this.resetDailyChallenges()
      }
    },
    
    // 重置每日挑战
    resetDailyChallenges() {
      this.dailyChallenges.forEach(challenge => {
        challenge.progress = 0
        challenge.completed = false
      })
      
      this.saveDailyChallenges()
    },
    
    // 保存每日挑战
    saveDailyChallenges() {
      const today = new Date().toDateString()
      uni.setStorageSync('daily_challenges', {
        date: today,
        challenges: this.dailyChallenges
      })
    },
    
    // 更新刷新时间
    updateRefreshTime() {
      const updateTime = () => {
        const now = new Date()
        const tomorrow = new Date(now)
        tomorrow.setDate(tomorrow.getDate() + 1)
        tomorrow.setHours(0, 0, 0, 0)
        
        const diff = tomorrow - now
        const hours = Math.floor(diff / (1000 * 60 * 60))
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((diff % (1000 * 60)) / 1000)
        
        this.refreshTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      }
      
      updateTime()
      setInterval(updateTime, 1000)
    },
    
    // 更新统计数据
    updateStats() {
      this.loadGameStats()
    },
    
    // 跳转到游戏
    goToGame(game) {
      const gameRoutes = {
        chess: '/pages/entertainment/chess/index',
        gobang: '/pages/entertainment/gobang/index',
        go: '/pages/entertainment/go/index',
        match3: '/pages/entertainment/match3/index'
      }
      
      const route = gameRoutes[game]
      if (route) {
        // 检查是否为首次游戏
        const firstGame = uni.getStorageSync('first_game_played')
        if (!firstGame) {
          uni.setStorageSync('first_game_played', true)
          this.redPacketStore.checkUserBehavior('play_first_game')
        }
        
        uni.navigateTo({
          url: route
        })
      } else {
        uni.showToast({
          title: '敬请期待',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.entertainment-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 游戏容器 */
.games-container {
  padding: 32rpx;
}

/* 游戏卡片 */
.game-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.game-cover {
  position: relative;
  height: 200rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-icon {
  font-size: 80rpx;
  color: white;
}

.chess-icon {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gobang-icon {
  color: #2c3e50;
}

.go-icon {
  color: #ecf0f1;
}

.match3-icon {
  background: linear-gradient(135deg, #e74c3c, #f39c12);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.game-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.game-badge.hot {
  background: #e74c3c;
}

.game-badge.new {
  background: #2ecc71;
}

.game-badge.coming {
  background: #95a5a6;
}

.game-info {
  padding: 24rpx;
}

.game-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.game-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 16rpx;
}

.game-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  font-size: 22rpx;
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 统计卡片 */
.game-stats-card {
  margin: 0 32rpx 32rpx;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.stats-period {
  font-size: 26rpx;
  color: var(--text-light);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--secondary-color);
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}

/* 成就系统 */
.achievements-card {
  margin: 0 32rpx 32rpx;
}

.achievements-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.achievements-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.achievements-count {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid var(--border-color);
  opacity: 0.5;
}

.achievement-item.unlocked {
  opacity: 1;
}

.achievement-item:last-child {
  border-bottom: none;
}

.achievement-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 4rpx;
}

.achievement-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

.achievement-reward {
  font-size: 24rpx;
  color: var(--success-color);
  font-weight: bold;
}

/* 每日挑战 */
.daily-challenge {
  margin: 0 32rpx;
}

.challenge-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.challenge-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.challenge-refresh {
  font-size: 24rpx;
  color: var(--text-light);
}

.challenge-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid var(--border-color);
}

.challenge-item.completed {
  opacity: 0.6;
}

.challenge-item:last-child {
  border-bottom: none;
}

.challenge-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 4rpx;
}

.challenge-progress {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

.challenge-reward {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.reward-icon {
  font-size: 24rpx;
}

.reward-amount {
  font-size: 26rpx;
  color: var(--danger-color);
  font-weight: bold;
}

@media (max-width: 500rpx) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>

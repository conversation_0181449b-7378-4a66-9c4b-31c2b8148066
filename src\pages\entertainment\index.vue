<template>
  <view class="entertainment-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-entertainment">
      <text class="header-title">娱乐游戏</text>
      <text class="header-subtitle">休闲娱乐，放松身心</text>
    </view>
    
    <!-- 游戏列表 -->
    <view class="games-grid">
      <!-- 中国象棋 -->
      <view class="game-card" @click="goToGame('chess')">
        <view class="game-icon">
          <text class="icon">♟️</text>
        </view>
        <view class="game-info">
          <text class="game-title">中国象棋</text>
          <text class="game-desc">经典象棋对弈，智慧博弈</text>
          <text class="game-status">可游玩</text>
        </view>
        <view class="game-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 五子棋 -->
      <view class="game-card" @click="goToGame('gobang')">
        <view class="game-icon">
          <text class="icon">⚫</text>
        </view>
        <view class="game-info">
          <text class="game-title">五子棋</text>
          <text class="game-desc">简单易学，趣味无穷</text>
          <text class="game-status">可游玩</text>
        </view>
        <view class="game-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
      
      <!-- 围棋 -->
      <view class="game-card coming-soon">
        <view class="game-icon">
          <text class="icon">⚪</text>
        </view>
        <view class="game-info">
          <text class="game-title">围棋</text>
          <text class="game-desc">千年智慧，博大精深</text>
          <text class="game-status">即将推出</text>
        </view>
        <view class="game-arrow">
          <text class="arrow">⏳</text>
        </view>
      </view>
      
      <!-- 消消乐 -->
      <view class="game-card coming-soon">
        <view class="game-icon">
          <text class="icon">🍭</text>
        </view>
        <view class="game-info">
          <text class="game-title">消消乐</text>
          <text class="game-desc">休闲益智，轻松愉快</text>
          <text class="game-status">即将推出</text>
        </view>
        <view class="game-arrow">
          <text class="arrow">⏳</text>
        </view>
      </view>
    </view>
    
    <!-- 游戏统计 -->
    <view class="stats-section">
      <view class="section-header">
        <text class="section-title">游戏统计</text>
      </view>
      
      <view class="stats-cards">
        <view class="stat-card">
          <text class="stat-number">{{ gameStats.totalGames }}</text>
          <text class="stat-label">总局数</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ gameStats.winRate }}%</text>
          <text class="stat-label">胜率</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ gameStats.playTime }}</text>
          <text class="stat-label">游戏时长</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      gameStats: {
        totalGames: 0,
        winRate: 0,
        playTime: '0分钟'
      }
    }
  },
  
  onLoad() {
    this.loadGameStats()
  },
  
  onShow() {
    this.loadGameStats()
  },
  
  methods: {
    // 跳转到游戏
    goToGame(game) {
      // 记录游戏使用
      this.recordGameUsage()
      
      const gameRoutes = {
        chess: '/pages/entertainment/chess/index',
        gobang: '/pages/entertainment/gobang/index'
      }
      
      const route = gameRoutes[game]
      if (route) {
        uni.navigateTo({
          url: route
        })
      } else {
        uni.showToast({
          title: '游戏即将推出',
          icon: 'none'
        })
      }
    },
    
    // 加载游戏统计
    loadGameStats() {
      const stats = uni.getStorageSync('game_stats') || {
        totalGames: 0,
        wins: 0,
        playTime: 0
      }
      
      this.gameStats = {
        totalGames: stats.totalGames,
        winRate: stats.totalGames > 0 ? Math.round((stats.wins / stats.totalGames) * 100) : 0,
        playTime: this.formatPlayTime(stats.playTime)
      }
    },
    
    // 格式化游戏时长
    formatPlayTime(seconds) {
      if (seconds < 60) {
        return `${seconds}秒`
      } else if (seconds < 3600) {
        return `${Math.floor(seconds / 60)}分钟`
      } else {
        return `${Math.floor(seconds / 3600)}小时`
      }
    },
    
    // 记录游戏使用
    recordGameUsage() {
      // 触发使用游戏任务
      this.$store.dispatch('redpacket/checkUserBehavior', 'play_game')
    }
  }
}
</script>

<style lang="scss" scoped>
.entertainment-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 游戏网格 */
.games-grid {
  padding: 32rpx;
}

.game-card {
  background: white;
  border-radius: 12px;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.game-card.coming-soon {
  opacity: 0.6;
  cursor: not-allowed;
}

.game-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ec4899, #f97316);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.game-info {
  flex: 1;
}

.game-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.game-desc {
  font-size: 26rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 8rpx;
}

.game-status {
  font-size: 24rpx;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.coming-soon .game-status {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.game-arrow {
  font-size: 32rpx;
  color: #6b7280;
}

/* 统计区域 */
.stats-section {
  padding: 0 32rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #ec4899;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}
</style>

const state = {
  // 用户红包数据
  userRedPackets: [],
  totalAmount: 0,
  totalCount: 0,
  
  // 签到数据
  signInData: {
    lastSignInDate: '',
    consecutiveDays: 0,
    totalSignInDays: 0
  },
  
  // 任务红包数据
  taskRedPackets: [
    {
      id: 'first_use',
      name: '首次使用工具',
      desc: '使用任意一个工具',
      amount: 5,
      claimed: false,
      type: 'task'
    },
    {
      id: 'use_game',
      name: '体验游戏',
      desc: '玩一局象棋或五子棋',
      amount: 8,
      claimed: false,
      type: 'task'
    },
    {
      id: 'continuous_signin',
      name: '连续签到',
      desc: '连续签到3天',
      amount: 15,
      claimed: false,
      type: 'task'
    }
  ],
  
  // 签到奖励配置
  signInRewards: [
    { day: 1, amount: 2, type: 'points' },
    { day: 2, amount: 3, type: 'points' },
    { day: 3, amount: 5, type: 'redpacket', special: true },
    { day: 4, amount: 3, type: 'points' },
    { day: 5, amount: 4, type: 'points' },
    { day: 6, amount: 6, type: 'points' },
    { day: 7, amount: 10, type: 'redpacket', special: true }
  ]
}

const mutations = {
  // 添加红包
  ADD_REDPACKET(state, redpacket) {
    state.userRedPackets.push({
      ...redpacket,
      id: Date.now() + Math.random(),
      claimTime: new Date().toISOString()
    })
    state.totalAmount += redpacket.amount
    state.totalCount += 1
    this.dispatch('redpacket/saveRedPacketData')
  },
  
  // 更新签到数据
  UPDATE_SIGNIN_DATA(state, data) {
    state.signInData = { ...state.signInData, ...data }
    this.dispatch('redpacket/saveRedPacketData')
  },
  
  // 完成任务红包
  COMPLETE_TASK_REDPACKET(state, taskId) {
    const task = state.taskRedPackets.find(t => t.id === taskId)
    if (task && !task.claimed) {
      task.claimed = true
      this.commit('redpacket/ADD_REDPACKET', {
        type: 'task',
        title: task.name,
        amount: task.amount,
        message: '恭喜完成任务！'
      })
    }
  },
  
  // 加载数据
  LOAD_REDPACKET_DATA(state, data) {
    if (data.userRedPackets) state.userRedPackets = data.userRedPackets
    if (data.totalAmount) state.totalAmount = data.totalAmount
    if (data.totalCount) state.totalCount = data.totalCount
    if (data.signInData) state.signInData = data.signInData
    if (data.taskRedPackets) state.taskRedPackets = data.taskRedPackets
  }
}

const actions = {
  // 初始化红包数据
  initRedPacketData({ commit }) {
    const data = uni.getStorageSync('redpacket_data')
    if (data) {
      commit('LOAD_REDPACKET_DATA', JSON.parse(data))
    }
  },
  
  // 保存红包数据
  saveRedPacketData({ state }) {
    const data = {
      userRedPackets: state.userRedPackets,
      totalAmount: state.totalAmount,
      totalCount: state.totalCount,
      signInData: state.signInData,
      taskRedPackets: state.taskRedPackets
    }
    uni.setStorageSync('redpacket_data', JSON.stringify(data))
  },
  
  // 领取红包
  claimRedPacket({ commit }, redpacket) {
    commit('ADD_REDPACKET', redpacket)
  },
  
  // 每日签到
  dailySignIn({ state, commit }) {
    const today = new Date().toDateString()
    const lastSignIn = state.signInData.lastSignInDate
    
    if (lastSignIn === today) {
      return { success: false, message: '今日已签到' }
    }
    
    let consecutiveDays = state.signInData.consecutiveDays
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (lastSignIn === yesterday.toDateString()) {
      consecutiveDays += 1
    } else {
      consecutiveDays = 1
    }
    
    // 重置连续签到（7天一个周期）
    if (consecutiveDays > 7) {
      consecutiveDays = 1
    }
    
    const reward = state.signInRewards[consecutiveDays - 1]
    
    commit('UPDATE_SIGNIN_DATA', {
      lastSignInDate: today,
      consecutiveDays: consecutiveDays,
      totalSignInDays: state.signInData.totalSignInDays + 1
    })
    
    // 如果是红包奖励，添加红包
    if (reward.type === 'redpacket') {
      commit('ADD_REDPACKET', {
        type: 'signin',
        title: '签到红包',
        amount: reward.amount,
        message: `第${consecutiveDays}天签到奖励`
      })
    }
    
    return { success: true, reward }
  },
  
  // 完成任务红包
  completeTaskRedPacket({ commit }, taskId) {
    commit('COMPLETE_TASK_REDPACKET', taskId)
    return { success: true }
  },
  
  // 检查用户行为
  checkUserBehavior({ state, dispatch }, behavior) {
    switch (behavior) {
      case 'use_any_tool':
        if (!state.taskRedPackets.find(t => t.id === 'first_use').claimed) {
          dispatch('completeTaskRedPacket', 'first_use')
        }
        break
      case 'play_game':
        if (!state.taskRedPackets.find(t => t.id === 'use_game').claimed) {
          dispatch('completeTaskRedPacket', 'use_game')
        }
        break
      case 'continuous_signin':
        if (state.signInData.consecutiveDays >= 3 && 
            !state.taskRedPackets.find(t => t.id === 'continuous_signin').claimed) {
          dispatch('completeTaskRedPacket', 'continuous_signin')
        }
        break
    }
  }
}

const getters = {
  // 是否今日已签到
  isTodaySignedIn: (state) => {
    const today = new Date().toDateString()
    return state.signInData.lastSignInDate === today
  },
  
  // 连续签到天数
  getConsecutiveDays: (state) => {
    return state.signInData.consecutiveDays
  },
  
  // 获取红包历史
  getRedPacketHistory: (state) => {
    return state.userRedPackets.sort((a, b) => new Date(b.claimTime) - new Date(a.claimTime))
  },
  
  // 获取可用任务红包
  getAvailableTaskRedPackets: (state) => {
    return state.taskRedPackets.filter(task => !task.claimed)
  },
  
  // 获取月度统计
  getMonthlyStats: (state) => {
    const thisMonth = new Date().getMonth()
    const thisYear = new Date().getFullYear()
    
    const monthlyPackets = state.userRedPackets.filter(packet => {
      const packetDate = new Date(packet.claimTime)
      return packetDate.getMonth() === thisMonth && packetDate.getFullYear() === thisYear
    })
    
    return {
      count: monthlyPackets.length,
      amount: monthlyPackets.reduce((sum, packet) => sum + packet.amount, 0)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

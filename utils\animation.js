/**
 * 动画工具类
 * 提供各种动画效果和工具函数
 */

export class AnimationUtils {
  
  /**
   * 创建弹跳动画
   * @param {string} selector - 元素选择器
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static bounce(selector, duration = 600) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration,
        timingFunction: 'ease-in-out'
      })
      
      // 弹跳序列
      animation.scale(0.3).step({ duration: 0 })
      animation.scale(1.1).step({ duration: duration * 0.4 })
      animation.scale(0.95).step({ duration: duration * 0.3 })
      animation.scale(1).step({ duration: duration * 0.3 })
      
      // 应用动画
      const query = uni.createSelectorQuery()
      query.select(selector).boundingClientRect((rect) => {
        if (rect) {
          // 这里需要在组件中实际应用动画
          setTimeout(resolve, duration)
        } else {
          resolve()
        }
      }).exec()
    })
  }
  
  /**
   * 创建淡入动画
   * @param {string} selector - 元素选择器
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static fadeIn(selector, duration = 400) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration,
        timingFunction: 'ease-out'
      })
      
      animation.opacity(0).step({ duration: 0 })
      animation.opacity(1).step({ duration: duration })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建滑入动画
   * @param {string} selector - 元素选择器
   * @param {string} direction - 滑入方向 (up, down, left, right)
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static slideIn(selector, direction = 'up', duration = 300) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration,
        timingFunction: 'ease-out'
      })
      
      let startTransform = ''
      switch (direction) {
        case 'up':
          startTransform = 'translateY(100%)'
          break
        case 'down':
          startTransform = 'translateY(-100%)'
          break
        case 'left':
          startTransform = 'translateX(100%)'
          break
        case 'right':
          startTransform = 'translateX(-100%)'
          break
      }
      
      animation.translate(startTransform).step({ duration: 0 })
      animation.translate(0, 0).step({ duration: duration })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建震动动画
   * @param {string} selector - 元素选择器
   * @param {number} intensity - 震动强度(px)
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static shake(selector, intensity = 10, duration = 500) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: 100,
        timingFunction: 'ease-in-out'
      })
      
      const steps = 5
      const stepDuration = duration / steps
      
      for (let i = 0; i < steps; i++) {
        const offset = i % 2 === 0 ? intensity : -intensity
        animation.translateX(offset).step({ duration: stepDuration })
      }
      
      animation.translateX(0).step({ duration: stepDuration })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建脉冲动画
   * @param {string} selector - 元素选择器
   * @param {number} scale - 缩放比例
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static pulse(selector, scale = 1.1, duration = 800) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration / 2,
        timingFunction: 'ease-in-out'
      })
      
      animation.scale(scale).step({ duration: duration / 2 })
      animation.scale(1).step({ duration: duration / 2 })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建旋转动画
   * @param {string} selector - 元素选择器
   * @param {number} degrees - 旋转角度
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static rotate(selector, degrees = 360, duration = 1000) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration,
        timingFunction: 'linear'
      })
      
      animation.rotate(degrees).step({ duration: duration })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建心跳动画
   * @param {string} selector - 元素选择器
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static heartbeat(selector, duration = 1000) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: 100,
        timingFunction: 'ease-in-out'
      })
      
      // 心跳序列
      animation.scale(1.1).step({ duration: 100 })
      animation.scale(1).step({ duration: 100 })
      animation.scale(1.1).step({ duration: 100 })
      animation.scale(1).step({ duration: 700 })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建弹性动画
   * @param {string} selector - 元素选择器
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static elastic(selector, duration = 1000) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration,
        timingFunction: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
      })
      
      animation.scale(0).step({ duration: 0 })
      animation.scale(1).step({ duration: duration })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建翻转动画
   * @param {string} selector - 元素选择器
   * @param {string} axis - 翻转轴 (X, Y)
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static flip(selector, axis = 'Y', duration = 600) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration / 2,
        timingFunction: 'ease-in'
      })
      
      if (axis === 'Y') {
        animation.rotateY(90).step({ duration: duration / 2 })
        animation.rotateY(0).step({ duration: duration / 2 })
      } else {
        animation.rotateX(90).step({ duration: duration / 2 })
        animation.rotateX(0).step({ duration: duration / 2 })
      }
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建缩放动画
   * @param {string} selector - 元素选择器
   * @param {number} fromScale - 起始缩放
   * @param {number} toScale - 结束缩放
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static scale(selector, fromScale = 0, toScale = 1, duration = 400) {
    return new Promise((resolve) => {
      const animation = uni.createAnimation({
        duration: duration,
        timingFunction: 'ease-out'
      })
      
      animation.scale(fromScale).step({ duration: 0 })
      animation.scale(toScale).step({ duration: duration })
      
      setTimeout(resolve, duration)
    })
  }
  
  /**
   * 创建连续动画序列
   * @param {Array} animations - 动画序列
   * @returns {Promise} 所有动画完成Promise
   */
  static sequence(animations) {
    return animations.reduce((promise, animation) => {
      return promise.then(() => animation())
    }, Promise.resolve())
  }
  
  /**
   * 创建并行动画
   * @param {Array} animations - 动画数组
   * @returns {Promise} 所有动画完成Promise
   */
  static parallel(animations) {
    return Promise.all(animations.map(animation => animation()))
  }
  
  /**
   * 延迟执行
   * @param {number} duration - 延迟时长(ms)
   * @returns {Promise} 延迟完成Promise
   */
  static delay(duration) {
    return new Promise(resolve => setTimeout(resolve, duration))
  }
  
  /**
   * 创建缓动函数
   * @param {string} type - 缓动类型
   * @returns {string} CSS缓动函数
   */
  static getEasing(type) {
    const easings = {
      'ease-in': 'cubic-bezier(0.42, 0, 1, 1)',
      'ease-out': 'cubic-bezier(0, 0, 0.58, 1)',
      'ease-in-out': 'cubic-bezier(0.42, 0, 0.58, 1)',
      'bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      'elastic': 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
      'back': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
    
    return easings[type] || 'ease'
  }
  
  /**
   * 创建关键帧动画
   * @param {string} name - 动画名称
   * @param {Array} keyframes - 关键帧数组
   * @param {number} duration - 动画时长(ms)
   * @returns {Object} 动画配置
   */
  static createKeyframes(name, keyframes, duration = 1000) {
    return {
      name: name,
      keyframes: keyframes,
      duration: duration,
      fillMode: 'forwards',
      iterationCount: 1
    }
  }
  
  /**
   * 应用CSS动画类
   * @param {string} selector - 元素选择器
   * @param {string} className - 动画类名
   * @param {number} duration - 动画时长(ms)
   * @returns {Promise} 动画完成Promise
   */
  static applyCSSAnimation(selector, className, duration = 1000) {
    return new Promise((resolve) => {
      const query = uni.createSelectorQuery()
      query.select(selector).boundingClientRect((rect) => {
        if (rect) {
          // 添加动画类
          // 这里需要在组件中实际操作DOM
          setTimeout(() => {
            // 移除动画类
            resolve()
          }, duration)
        } else {
          resolve()
        }
      }).exec()
    })
  }
  
  /**
   * 检查是否支持动画
   * @returns {boolean} 是否支持动画
   */
  static isAnimationSupported() {
    // 在小程序环境中，动画支持情况
    // #ifdef MP-WEIXIN
    return true
    // #endif
    
    // #ifdef APP-PLUS
    return true
    // #endif
    
    // #ifdef H5
    return typeof document !== 'undefined' && 'animate' in document.createElement('div')
    // #endif
    
    return false
  }
  
  /**
   * 获取动画性能信息
   * @returns {Object} 性能信息
   */
  static getPerformanceInfo() {
    return {
      isSupported: this.isAnimationSupported(),
      preferredDuration: 300, // 推荐动画时长
      maxConcurrent: 5, // 最大并发动画数
      reducedMotion: false // 是否启用减少动画
    }
  }
}

/**
 * 预定义动画配置
 */
export const AnimationPresets = {
  // 红包相关动画
  redPacketAppear: {
    type: 'bounce',
    duration: 600,
    easing: 'ease-in-out'
  },
  
  redPacketOpen: {
    type: 'scale',
    from: 1,
    to: 1.2,
    duration: 300,
    easing: 'ease-out'
  },
  
  coinDrop: {
    type: 'sequence',
    animations: [
      { type: 'translateY', from: -50, to: 0, duration: 500 },
      { type: 'bounce', duration: 200 }
    ]
  },
  
  // 页面切换动画
  pageSlideIn: {
    type: 'slideIn',
    direction: 'right',
    duration: 300,
    easing: 'ease-out'
  },
  
  pageSlideOut: {
    type: 'slideOut',
    direction: 'left',
    duration: 300,
    easing: 'ease-in'
  },
  
  // 按钮动画
  buttonPress: {
    type: 'scale',
    from: 1,
    to: 0.95,
    duration: 100,
    easing: 'ease-in-out'
  },
  
  buttonHover: {
    type: 'translateY',
    from: 0,
    to: -2,
    duration: 200,
    easing: 'ease-out'
  },
  
  // 加载动画
  loading: {
    type: 'rotate',
    degrees: 360,
    duration: 1000,
    iterationCount: 'infinite',
    easing: 'linear'
  },
  
  pulse: {
    type: 'pulse',
    scale: 1.1,
    duration: 1000,
    iterationCount: 'infinite',
    easing: 'ease-in-out'
  }
}

{"warning": "Warning⚠", "syntaxError": "Syntax Error❌", "compilerVersion": "Compiler version", "compiling": "Compiling...", "see": "see", "platform": "platform", "plugin": "plugin", "performingHotReload": "Performing hot reload...", "cliShared.parseJsonFailed": "Parsing failed", "cliShared.doesNotExist": "does not exist", "cliShared.pagesJsonError": "pages.json page configuration error, has been ignored, see {{0}}", "cliShared.requireReturnJsonObject": "require return a json object", "cliShared.requireExportFunction": "Function must be exported", "cliShared.easycomConflict": "easycom component conflict：", "cliShared.noFoundPlatformPlugin": "Missing platform {{0}} plugin", "cliShared.extendOnlySupportH5": "Currently only supports expansion based on the h5 platform", "cliShared.supportPlatform": "{{0}} support the following platforms {{1}}", "cliShared.requireConfigUniPlatform": "{{0}} does not exist, you must configure the env->UNI_PLATFORM base platform", "cliShared.missingNameAttribute": "{{0}} missing name property", "cliShared.missingUniConfig": "{{0}} missing uni.config.js", "migration.errorOnlySupportConvert": "Error: {{0}} conversion is currently supported", "migration.errorInputNotExists": "Error: '{{0}}' not exist", "migration.errorCannotConvert": "Error: '{{0}}' does not support conversion", "migration.errorConvertRequireFileUrl": "Error: Single file conversion requires {{0}} file url", "mpWeChat.onlySupportDestructuringSlot": "Currently only supports destructuring slot {{0}}, such as {{1}}", "mpWeChat.slotPropNoSupportReanme": "Deconstructing the slot Prop, does not support renaming {{0}} to {{1}}, the performance will be affected after renaming", "uniStat.missingParameter": "Missing [eventName] parameter", "uniStat.parameterLengthLess": "Parameter length cannot be greater than", "uniStat.parameterTypeErrrorString": "Parameter type error, it can only be of type String", "uniStat.parameterTypeErrrorStringOrObject": "Parameter type error, Only supports String or Object type", "uniStat.hasTitleOptionString": "When the parameter is title, the [options] parameter can only be of type String", "templateCompiler.noH5KeyNoSupportExpression": "Non-h5 platforms: key does not support expression {{0}}, for details, please refer to: {{1}}", "templateCompiler.notCurrentlySupportScopedSlot": "Not currently supported scoped slot {{0}}", "templateCompiler.idAttribNotAllowInCustomComponentProps": "id is reserved as a property name and is not allowed to be defined as props in custom component {{0}}", "templateCompiler.notSupportDynamicSlotName": "{{0}} Does not support dynamic slot names, please use scopedSlotsCompiler: augmented", "templateCompiler.forNestedIndexNameNoArrowRepeat": "{{0}} When v-for is nested, the index name {{1}} is not allowed to be repeated", "templateCompiler.noSupportSyntax": "Does not support {{0}} syntax", "pluginHbuilderx.plaseHXCompileAppPlatform": "Please use HBuilderX to compile and run to the app-plus platform", "pluginHbuilderx.hxBuildFailed": "Build failed: HBuilderX installation directory cannot include special characters such as {{0}}", "pluginHbuilderx.nvueCssWarning": "The following css is not supported in nvue. If the global or public style is affected, it is recommended to write the warning style in the conditional compilation of ifndef APP-PLUS-NVUE. The details are as follows:", "pluginUni.runDebugMode": "Please note that in running mode, due to log output, sourcemap, and uncompressed source code, the performance and package size are not as good as release mode.", "pluginUni.runDebugModeNvue": "Especially the sourcemap of app-nvue has a greater impact", "pluginUni.runDebugModeMP": "To officially release, please click the release menu or use the cli release command to release", "pluginUni.compileToMpPluginOnlySupportPlatform": "Compile to mini-program plug-in only supports WeChat mini-program and AliPay mini-program", "pluginUni.startCompileProjectToPlatform": "Start to compile the current project to the {{0}} {{1}}...", "pluginUni.fileNoExistsCheckAfterRetry": "{{0}} file does not exist, please check and try again", "pluginUni.entryDileNoExistsCheckAfterRetry": "{{0}} The entry file does not exist, please check and try again", "pluginUni.nvueCompileModeForDetail": "Current NVUE compile mode {{0}}. see: {{1}}", "pluginUni.currentProjectDefaultSpaceId": "The default service space spaceId used by uniCloud of the current project is: {{0}}", "pluginUni.unicloudReleaseH5": "To release H5, you need to operate on the uniCloud web console and bind a secure domain name, otherwise it will be inaccessible due to cross-domain issues. Tutorial reference：{{0}}", "pluginUni.unicloudShowedRunByHBuilderX": "The current project uses uniCloud. In order to avoid the cross-domain problem of cloud function calls, it is recommended to debug in the HBuilderX built-in browser. If you use an external browser, you need to handle cross-domain. See details: {{0}}", "pluginUni.pleaseSpecifyPluginName": "Please specify the plugin name", "pluginUni.pluginNameNotExist": "Plug-in name does not exist", "pluginUni.pluginIllegal": "The plugin is illegal", "pluginUni.uniStatisticsNoAppid": "The current application is not configured with Appid, and uni statistics cannot be used. For details, see {{0}}", "pluginUni.uniStatisticsNoVersion": "The uni statistics version is not configured. The default version is 1.0.uni statistics version 2.0 is recommended, private deployment data is more secure and code is open source and customizable. details:{{0}} ", "pluginUni.pleaseConfigScriptName": "Please specify the script name under package.json->uni-app->scripts", "pluginUni.mpBrowserKernelDifference": "There are differences in the implementation mechanism of the browser kernels and custom components of each mini-program, and there may be compatibility issues with styles and layouts, please refer to: {{0}}", "mpLoader.firstParameterNeedStaticString": "The first parameter of {{0}} must be a static string", "mpLoader.requireTwoParameter": "{{0}} requires two parameters", "mpLoader.findFail": "{{0}} find fail", "mpLoader.componentReferenceError": "Component {{0}} reference error", "mpLoader.componentReferenceErrorOnlySupportImport": "Component {{0}} reference error, only supports import components", "pagesLoader.pagesNodeCannotNull": "Pages node in pages.json cannot be empty", "pagesLoader.nvueFirstPageStartModeIsFast": "App startup mode: fast. For details, see: {{0}}", "pagesLoader.pagesTabbarMinItem2": "{{0}} must contain at least 2 items", "pagesLoader.needInPagesNode": "{{0}} needs to be in the pages array", "i18n.fallbackLocale.default": "fallbackLocale is missing in manifest.json, use：{{locale}}", "i18n.fallbackLocale.missing": "./local/{locale}.json is missing", "prompt.run.message": "Run method: open {{devtools}}, import {{outputDir}} run.", "prompt.run.devtools.app-plus": "HBuilderX", "prompt.run.devtools.mp-alipay": "Alipay Mini Program Devtools", "prompt.run.devtools.mp-baidu": "Baidu Mini Program Devtools", "prompt.run.devtools.mp--kuaishou": "Kuaishou Mini Program Devtools", "prompt.run.devtools.mp-lark": "Lark Mini Program Devtools", "prompt.run.devtools.mp-qq": "QQ Mini Program Devtools", "prompt.run.devtools.mp-toutiao": "Douyin Mini Program Devtools", "prompt.run.devtools.mp-weixin": "Weixin Mini Program Devtools", "prompt.run.devtools.mp-jd": "Jingdong Mini Program Devtools", "prompt.run.devtools.mp-xhs": "Xiaohongshu Mini Program Devtools", "prompt.run.devtools.quickapp-webview": "Quick App Alliance Devtools | Huawei Quick App Devtools", "prompt.run.devtools.quickapp-webview-huawei": "Huawei Quick App Devtools", "prompt.run.devtools.quickapp-webview-union": "Quick App Alliance Devtools"}
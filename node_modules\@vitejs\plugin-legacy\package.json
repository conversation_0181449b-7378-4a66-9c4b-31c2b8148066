{"name": "@vitejs/plugin-legacy", "version": "4.1.1", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "keywords": ["frontend", "vite", "vite-plugin", "@vitejs/plugin-legacy"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts", "prepublishOnly": "npm run build"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-legacy"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-legacy#readme", "funding": "https://github.com/vitejs/vite?sponsor=1", "dependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "browserslist": "^4.21.9", "core-js": "^3.31.1", "magic-string": "^0.30.1", "regenerator-runtime": "^0.13.11", "systemjs": "^6.14.1"}, "peerDependencies": {"terser": "^5.4.0", "vite": "^4.0.0"}, "devDependencies": {"acorn": "^8.10.0", "picocolors": "^1.0.0", "vite": "workspace:*"}}
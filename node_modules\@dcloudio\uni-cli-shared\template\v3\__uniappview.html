<!DOCTYPE html>
<html lang="zh-CN">

  <head>
    <meta charset="UTF-8" />
    <script>
      var __UniViewStartTime__ = Date.now();
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title>View</title>
    <link rel="stylesheet" href="view.css" />
  </head>

  <body>
    <div id="app"></div>
    <script src="__uniappes6.js"></script>
    <script src="view.umd.min.js"></script>
    <script src="app-view.js"></script>
  </body>

</html>

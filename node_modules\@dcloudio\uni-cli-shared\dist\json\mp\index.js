"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseMiniProgramProjectJson = exports.parseMiniProgramPagesJson = exports.mergeMiniProgramAppJson = void 0;
__exportStar(require("./jsonFile"), exports);
var pages_1 = require("./pages");
Object.defineProperty(exports, "mergeMiniProgramAppJson", { enumerable: true, get: function () { return pages_1.mergeMiniProgramAppJson; } });
Object.defineProperty(exports, "parseMiniProgramPagesJson", { enumerable: true, get: function () { return pages_1.parseMiniProgramPagesJson; } });
var project_1 = require("./project");
Object.defineProperty(exports, "parseMiniProgramProjectJson", { enumerable: true, get: function () { return project_1.parseMiniProgramProjectJson; } });

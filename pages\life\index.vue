<template>
  <view class="life-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-life">
      <text class="header-title">生活助手</text>
      <text class="header-subtitle">便民服务，贴心生活</text>
    </view>
    
    <!-- 工具分类 -->
    <view class="tools-container">
      <!-- 日常计算 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">日常计算</text>
          <text class="category-desc">生活中的各种计算工具</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('calculator')">
            <view class="tool-icon" style="background: var(--primary-color);">
              <text class="icon">🧮</text>
            </view>
            <text class="tool-title">计算器</text>
            <text class="tool-desc">科学计算器</text>
          </view>
          
          <view class="tool-card" @click="goToTool('unit-convert')">
            <view class="tool-icon" style="background: var(--success-color);">
              <text class="icon">🔄</text>
            </view>
            <text class="tool-title">单位转换</text>
            <text class="tool-desc">长度重量转换</text>
          </view>
          
          <view class="tool-card" @click="goToTool('loan-calc')">
            <view class="tool-icon" style="background: var(--warning-color);">
              <text class="icon">🏦</text>
            </view>
            <text class="tool-title">房贷计算</text>
            <text class="tool-desc">房贷利息计算</text>
          </view>
          
          <view class="tool-card" @click="goToTool('tax-calc')">
            <view class="tool-icon" style="background: var(--danger-color);">
              <text class="icon">💰</text>
            </view>
            <text class="tool-title">个税计算</text>
            <text class="tool-desc">个人所得税</text>
          </view>
        </view>
      </view>
      
      <!-- 健康生活 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">健康生活</text>
          <text class="category-desc">关注健康，享受生活</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('bmi-calc')">
            <view class="tool-icon" style="background: var(--success-color);">
              <text class="icon">⚖️</text>
            </view>
            <text class="tool-title">BMI计算</text>
            <text class="tool-desc">身体质量指数</text>
          </view>
          
          <view class="tool-card" @click="goToTool('water-reminder')">
            <view class="tool-icon" style="background: var(--primary-color);">
              <text class="icon">💧</text>
            </view>
            <text class="tool-title">喝水提醒</text>
            <text class="tool-desc">健康饮水助手</text>
          </view>
          
          <view class="tool-card" @click="goToTool('sleep-tracker')">
            <view class="tool-icon" style="background: var(--secondary-color);">
              <text class="icon">😴</text>
            </view>
            <text class="tool-title">睡眠记录</text>
            <text class="tool-desc">睡眠质量追踪</text>
          </view>
          
          <view class="tool-card" @click="goToTool('calorie-calc')">
            <view class="tool-icon" style="background: var(--warning-color);">
              <text class="icon">🍎</text>
            </view>
            <text class="tool-title">卡路里</text>
            <text class="tool-desc">热量计算器</text>
          </view>
        </view>
      </view>
      
      <!-- 出行助手 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">出行助手</text>
          <text class="category-desc">便捷出行，智能导航</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('weather')">
            <view class="tool-icon" style="background: var(--primary-color);">
              <text class="icon">🌤️</text>
            </view>
            <text class="tool-title">天气查询</text>
            <text class="tool-desc">实时天气预报</text>
          </view>
          
          <view class="tool-card" @click="goToTool('oil-price')">
            <view class="tool-icon" style="background: var(--danger-color);">
              <text class="icon">⛽</text>
            </view>
            <text class="tool-title">油价查询</text>
            <text class="tool-desc">全国油价信息</text>
          </view>
          
          <view class="tool-card" @click="goToTool('express')">
            <view class="tool-icon" style="background: var(--success-color);">
              <text class="icon">📦</text>
            </view>
            <text class="tool-title">快递查询</text>
            <text class="tool-desc">物流信息追踪</text>
          </view>
          
          <view class="tool-card" @click="goToTool('traffic')">
            <view class="tool-icon" style="background: var(--warning-color);">
              <text class="icon">🚗</text>
            </view>
            <text class="tool-title">违章查询</text>
            <text class="tool-desc">车辆违章信息</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 今日小贴士 -->
    <view class="daily-tips card">
      <view class="tips-header">
        <text class="tips-title">今日小贴士</text>
        <text class="tips-refresh" @click="refreshTip">🔄</text>
      </view>
      <view class="tips-content">
        <view class="tip-icon">💡</view>
        <text class="tip-text">{{ currentTip }}</text>
      </view>
    </view>
    
    <!-- 快捷功能 -->
    <view class="quick-actions card">
      <view class="actions-header">
        <text class="actions-title">快捷功能</text>
      </view>
      <view class="actions-grid">
        <view class="action-item" @click="quickAction('scan')">
          <view class="action-icon">📱</view>
          <text class="action-text">扫一扫</text>
        </view>
        <view class="action-item" @click="quickAction('flashlight')">
          <view class="action-icon">🔦</view>
          <text class="action-text">手电筒</text>
        </view>
        <view class="action-item" @click="quickAction('ruler')">
          <view class="action-icon">📏</view>
          <text class="action-text">尺子</text>
        </view>
        <view class="action-item" @click="quickAction('level')">
          <view class="action-icon">📐</view>
          <text class="action-text">水平仪</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTip: '',
      
      dailyTips: [
        '每天喝8杯水有助于保持身体健康',
        '适量运动可以提高免疫力',
        '保持良好的睡眠习惯很重要',
        '多吃蔬菜水果，营养更均衡',
        '定期体检，预防疾病',
        '保持心情愉快，生活更美好',
        '合理安排时间，提高效率',
        '学会放松，缓解压力',
        '培养兴趣爱好，丰富生活',
        '与家人朋友多交流，增进感情'
      ]
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.loadDailyTip()
    },
    
    // 加载每日小贴士
    loadDailyTip() {
      const today = new Date().getDate()
      const tipIndex = today % this.dailyTips.length
      this.currentTip = this.dailyTips[tipIndex]
    },
    
    // 刷新小贴士
    refreshTip() {
      const randomIndex = Math.floor(Math.random() * this.dailyTips.length)
      this.currentTip = this.dailyTips[randomIndex]
      
      uni.showToast({
        title: '已刷新',
        icon: 'success',
        duration: 1000
      })
    },
    
    // 跳转到工具
    goToTool(toolId) {
      const toolRoutes = {
        'calculator': '/pages/life/calculator/index',
        'unit-convert': '/pages/life/tools/unit-convert',
        'loan-calc': '/pages/life/tools/loan-calc',
        'tax-calc': '/pages/life/tools/tax-calc',
        'bmi-calc': '/pages/life/health/bmi',
        'water-reminder': '/pages/life/health/water',
        'sleep-tracker': '/pages/life/health/sleep',
        'calorie-calc': '/pages/life/health/calorie',
        'weather': '/pages/life/travel/weather',
        'oil-price': '/pages/life/travel/oil-price',
        'express': '/pages/life/travel/express',
        'traffic': '/pages/life/travel/traffic'
      }
      
      const route = toolRoutes[toolId]
      if (route) {
        uni.navigateTo({
          url: route
        })
      } else {
        uni.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    },
    
    // 快捷功能
    quickAction(action) {
      switch (action) {
        case 'scan':
          // #ifdef APP-PLUS
          uni.scanCode({
            success: (res) => {
              uni.showModal({
                title: '扫描结果',
                content: res.result,
                showCancel: false
              })
            }
          })
          // #endif
          
          // #ifndef APP-PLUS
          uni.showToast({
            title: '该功能需要在APP中使用',
            icon: 'none'
          })
          // #endif
          break
          
        case 'flashlight':
          uni.showToast({
            title: '手电筒功能开发中',
            icon: 'none'
          })
          break
          
        case 'ruler':
          uni.showToast({
            title: '尺子功能开发中',
            icon: 'none'
          })
          break
          
        case 'level':
          uni.showToast({
            title: '水平仪功能开发中',
            icon: 'none'
          })
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.life-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 工具容器 */
.tools-container {
  padding: 32rpx;
}

/* 工具分类 */
.tool-category {
  margin-bottom: 48rpx;
}

.category-header {
  margin-bottom: 24rpx;
}

.category-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.category-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 今日小贴士 */
.daily-tips {
  margin: 0 32rpx 32rpx;
}

.tips-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.tips-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.tips-refresh {
  font-size: 32rpx;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.tips-refresh:hover {
  transform: rotate(180deg);
}

.tips-content {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.tip-icon {
  font-size: 40rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-color);
  line-height: 1.6;
}

/* 快捷功能 */
.quick-actions {
  margin: 0 32rpx;
}

.actions-header {
  margin-bottom: 24rpx;
}

.actions-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: var(--bg-color);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: #f1f5f9;
  transform: translateY(-4rpx);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-color);
  text-align: center;
}

@media (max-width: 500rpx) {
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>

{"name": "@dcloudio/uni-app-uts", "version": "3.0.0-3081220230817001", "description": "uni-app-uts", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["bin", "dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-app-uts"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "dependencies": {"@babel/parser": "^7.16.4", "@babel/types": "^7.20.7", "@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/uni-i18n": "3.0.0-3081220230817001", "@dcloudio/uni-nvue-styler": "3.0.0-3081220230817001", "@dcloudio/uni-shared": "3.0.0-3081220230817001", "@rollup/pluginutils": "^4.2.0", "@vue/compiler-core": "3.2.47", "@vue/compiler-sfc": "3.2.47", "@vue/shared": "3.2.47", "debug": "^4.3.3", "es-module-lexer": "^1.2.1", "fs-extra": "^10.0.0", "picocolors": "^1.0.0", "source-map": "^0.6.1"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13"}}
/* 全局样式文件 */

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

page {
  background-color: var(--bg-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

/* 容器样式 */
.container {
  padding: 32rpx;
  min-height: 100vh;
}

.page-container {
  padding: 32rpx;
  background: var(--bg-color);
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.card-content {
  color: var(--text-light);
  line-height: 1.6;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--radius);
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #5856eb;
  transform: translateY(-2rpx);
}

.btn-secondary {
  background: var(--secondary-color);
  color: white;
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius);
  font-size: 32rpx;
  background: var(--card-bg);
  color: var(--text-color);
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color);
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 32rpx;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 工具卡片 */
.tool-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  padding: 32rpx;
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.tool-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}

.tool-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16rpx;
}

.tool-desc {
  font-size: 26rpx;
  color: var(--text-light);
  line-height: 1.4;
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 128rpx 32rpx;
  color: var(--text-light);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 500rpx) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .container,
  .page-container {
    padding: 24rpx;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

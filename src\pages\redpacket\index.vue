<template>
  <view class="redpacket-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-redpacket">
      <text class="header-title">我的红包</text>
      <text class="header-subtitle">每日签到领红包，完成任务得奖励</text>
    </view>
    
    <!-- 签到区域 -->
    <view class="signin-section">
      <view class="card signin-card">
        <view class="signin-header">
          <text class="signin-title">每日签到</text>
          <text class="signin-streak">连续{{ consecutiveDays }}天</text>
        </view>
        
        <!-- 签到日历 -->
        <view class="signin-calendar">
          <view 
            v-for="(day, index) in signInCalendar" 
            :key="index"
            class="calendar-day"
            :class="{
              'signed': day.signed,
              'today': day.isToday,
              'special': day.special
            }"
          >
            <text class="day-number">{{ day.day }}</text>
            <text class="day-reward">{{ day.reward }}{{ day.type === 'redpacket' ? '元' : '分' }}</text>
            <text class="day-icon" v-if="day.signed">✓</text>
          </view>
        </view>
        
        <!-- 签到按钮 -->
        <button 
          class="btn btn-danger btn-block signin-btn"
          :class="{ 'btn-disabled': isTodaySignedIn }"
          @click="handleSignIn"
        >
          {{ isTodaySignedIn ? '今日已签到' : '立即签到' }}
        </button>
      </view>
    </view>
    
    <!-- 任务红包区域 -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">任务红包</text>
        <text class="section-desc">完成任务获得红包奖励</text>
      </view>
      
      <view class="card">
        <view 
          v-for="task in taskRedPackets" 
          :key="task.id"
          class="task-item"
          :class="{ 'completed': task.claimed }"
        >
          <view class="task-info">
            <text class="task-name">{{ task.name }}</text>
            <text class="task-desc">{{ task.desc }}</text>
          </view>
          <view class="task-reward">
            <text class="reward-amount">{{ task.amount }}元</text>
            <button 
              class="btn btn-small claim-btn"
              :class="task.claimed ? 'btn-disabled' : 'btn-primary'"
              @click="claimTask(task)"
            >
              {{ task.claimed ? '已领取' : '领取' }}
            </button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="section-header">
        <text class="section-title">红包统计</text>
      </view>
      
      <view class="stats-cards">
        <view class="stat-card">
          <text class="stat-number">{{ redPacketStats.amount }}</text>
          <text class="stat-label">本月红包</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ totalCount }}</text>
          <text class="stat-label">红包个数</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ totalAmount }}</text>
          <text class="stat-label">累计红包</text>
        </view>
      </view>
    </view>
    
    <!-- 红包记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">红包记录</text>
        <text class="clear-btn" @click="clearHistory">清空记录</text>
      </view>
      
      <view class="card" v-if="redPacketHistory.length > 0">
        <view 
          class="history-item" 
          v-for="packet in redPacketHistory.slice(0, 10)" 
          :key="packet.id"
        >
          <view class="packet-icon" :style="{ background: getPacketColor(packet.type) }">
            🧧
          </view>
          <view class="packet-info">
            <text class="packet-title">{{ packet.title }}</text>
            <text class="packet-time">{{ formatTime(packet.claimTime) }}</text>
          </view>
          <view class="packet-amount">
            <text class="amount-text">+{{ packet.amount }}元</text>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else>
        <view class="empty-icon">🧧</view>
        <text class="empty-text">暂无红包记录</text>
        <text class="empty-desc">完成任务或签到获得红包吧</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex'

export default {
  data() {
    return {
      showRedPacket: false,
      currentRedPacket: null
    }
  },
  
  computed: {
    ...mapState('redpacket', [
      'userRedPackets',
      'totalAmount', 
      'totalCount',
      'taskRedPackets',
      'signInRewards'
    ]),
    
    ...mapGetters('redpacket', [
      'isTodaySignedIn',
      'getConsecutiveDays',
      'getRedPacketHistory',
      'getMonthlyStats'
    ]),
    
    consecutiveDays() {
      return this.getConsecutiveDays
    },
    
    redPacketStats() {
      return this.getMonthlyStats
    },
    
    redPacketHistory() {
      return this.getRedPacketHistory
    },
    
    signInCalendar() {
      const calendar = []
      const rewards = this.signInRewards
      
      rewards.forEach((reward, index) => {
        const dayData = {
          day: reward.day,
          reward: reward.amount,
          type: reward.type,
          special: reward.special || false,
          signed: index < this.consecutiveDays,
          isToday: index === this.consecutiveDays && !this.isTodaySignedIn
        }
        calendar.push(dayData)
      })
      
      return calendar
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.updateData()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.$store.dispatch('redpacket/initRedPacketData')
    },
    
    // 更新数据
    updateData() {
      // 数据会通过computed自动更新
    },
    
    // 处理签到
    async handleSignIn() {
      if (this.isTodaySignedIn) {
        uni.showToast({
          title: '今日已签到',
          icon: 'none'
        })
        return
      }
      
      const result = await this.$store.dispatch('redpacket/dailySignIn')
      
      if (result.success) {
        // 如果获得红包，显示红包弹窗
        if (result.reward.type === 'redpacket') {
          setTimeout(() => {
            this.showSignInRedPacket(result.reward)
          }, 500)
        } else {
          uni.showToast({
            title: `签到成功！获得${result.reward.amount}积分`,
            icon: 'success'
          })
        }
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    },
    
    // 显示签到红包
    showSignInRedPacket(reward) {
      uni.$emit('show_redpacket', {
        type: 'signin',
        amount: reward.amount,
        title: '签到红包',
        message: `恭喜您获得第${this.consecutiveDays}天签到奖励！`,
        animation: 'bounce'
      })
    },
    
    // 领取任务红包
    claimTask(task) {
      if (task.claimed) {
        uni.showToast({
          title: '已经领取过了',
          icon: 'none'
        })
        return
      }
      
      const result = this.$store.dispatch('redpacket/completeTaskRedPacket', task.id)
      
      if (result.success) {
        // 显示红包弹窗
        uni.$emit('show_redpacket', {
          type: 'task',
          amount: task.amount,
          title: task.name,
          message: '恭喜您完成任务！',
          animation: 'pulse'
        })
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    },
    
    // 获取红包颜色
    getPacketColor(type) {
      const colors = {
        newuser: '#ff6b6b',
        signin: '#4ecdc4',
        task: '#45b7d1',
        special: '#f9ca24'
      }
      return colors[type] || '#ff6b6b'
    },
    
    // 格式化时间
    formatTime(timeString) {
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else if (diff < 2592000000) {
        return `${Math.floor(diff / 86400000)}天前`
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 清空记录
    clearHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有红包记录吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            this.$store.commit('redpacket/LOAD_REDPACKET_DATA', {
              userRedPackets: [],
              totalAmount: 0,
              totalCount: 0
            })
            this.$store.dispatch('redpacket/saveRedPacketData')
            
            uni.showToast({
              title: '已清空记录',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.redpacket-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 签到区域 */
.signin-section {
  padding: 32rpx;
}

.signin-card {
  text-align: center;
}

.signin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.signin-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.signin-streak {
  font-size: 26rpx;
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 签到日历 */
.signin-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.calendar-day {
  position: relative;
  padding: 20rpx 8rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.calendar-day.signed {
  background: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
}

.calendar-day.today {
  background: rgba(220, 38, 38, 0.1);
  border-color: #dc2626;
}

.calendar-day.special {
  background: linear-gradient(135deg, #f9ca24, #f0932b);
  color: white;
}

.day-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 4rpx;
}

.calendar-day.special .day-number {
  color: white;
}

.day-reward {
  font-size: 20rpx;
  color: #6b7280;
  display: block;
}

.calendar-day.special .day-reward {
  color: white;
}

.day-icon {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  font-size: 16rpx;
  color: #10b981;
}

/* 任务区域 */
.tasks-section {
  padding: 0 32rpx 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.section-desc {
  font-size: 26rpx;
  color: #6b7280;
}

.clear-btn {
  font-size: 26rpx;
  color: #6b7280;
  cursor: pointer;
}

/* 任务列表 */
.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.task-item.completed {
  opacity: 0.6;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4rpx;
}

.task-desc {
  font-size: 26rpx;
  color: #6b7280;
  display: block;
}

.task-reward {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.reward-amount {
  font-size: 28rpx;
  color: #dc2626;
  font-weight: bold;
}

.claim-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

/* 统计卡片 */
.stats-section {
  padding: 0 32rpx 32rpx;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #dc2626;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 历史记录 */
.history-section {
  padding: 0 32rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #e5e7eb;
}

.history-item:last-child {
  border-bottom: none;
}

.packet-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.packet-info {
  flex: 1;
}

.packet-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4rpx;
}

.packet-time {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
}

.packet-amount {
  text-align: right;
}

.amount-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #dc2626;
}
</style>

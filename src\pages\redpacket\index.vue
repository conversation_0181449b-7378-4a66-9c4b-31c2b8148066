<template>
  <view class="redpacket-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-redpacket">
      <text class="header-title">我的红包</text>
      <text class="header-subtitle">累计收益 {{ totalAmount }}分</text>
    </view>
    
    <!-- 签到区域 -->
    <view class="signin-section">
      <view class="signin-card card">
        <view class="signin-header">
          <text class="signin-title">每日签到</text>
          <text class="signin-streak">连续{{ consecutiveDays }}天</text>
        </view>
        
        <view class="signin-calendar">
          <view 
            class="calendar-day" 
            v-for="(day, index) in signInCalendar" 
            :key="index"
            :class="{ 
              'signed': day.signed, 
              'today': day.isToday,
              'special': day.special 
            }"
          >
            <text class="day-number">{{ day.day }}</text>
            <text class="day-reward">{{ day.reward }}{{ day.type === 'redpacket' ? '分' : '积分' }}</text>
            <view class="day-icon" v-if="day.signed">✓</view>
          </view>
        </view>
        
        <button 
          class="signin-btn btn btn-danger btn-block" 
          @click="handleSignIn"
          :disabled="isTodaySignedIn"
        >
          {{ isTodaySignedIn ? '今日已签到' : '立即签到' }}
        </button>
      </view>
    </view>
    
    <!-- 任务红包 -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">任务红包</text>
        <text class="section-desc">完成任务即可获得红包奖励</text>
      </view>
      
      <view class="tasks-list">
        <view 
          class="task-item card" 
          v-for="task in taskRedPackets" 
          :key="task.id"
          :class="{ 'completed': task.claimed }"
        >
          <view class="task-info">
            <text class="task-name">{{ task.name }}</text>
            <text class="task-desc">{{ task.description }}</text>
          </view>
          <view class="task-reward">
            <text class="reward-amount">{{ task.amount }}分</text>
            <button 
              class="claim-btn btn btn-small"
              :class="task.claimed ? 'btn-outline' : 'btn-primary'"
              @click="claimTask(task)"
              :disabled="task.claimed"
            >
              {{ task.claimed ? '已领取' : '领取' }}
            </button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 红包统计 */
    <view class="stats-section">
      <view class="stats-cards">
        <view class="stat-card">
          <text class="stat-number">{{ redPacketStats.count }}</text>
          <text class="stat-label">本月红包</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ redPacketStats.amount }}</text>
          <text class="stat-label">本月收益</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ totalCount }}</text>
          <text class="stat-label">累计红包</text>
        </view>
      </view>
    </view>
    
    <!-- 红包记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">红包记录</text>
        <text class="clear-btn" @click="clearHistory">清空记录</text>
      </view>
      
      <view class="history-list" v-if="redPacketHistory.length > 0">
        <view 
          class="history-item" 
          v-for="packet in redPacketHistory" 
          :key="packet.id"
        >
          <view class="packet-icon" :style="{ background: getPacketColor(packet.type) }">
            🧧
          </view>
          <view class="packet-info">
            <text class="packet-title">{{ packet.title }}</text>
            <text class="packet-time">{{ formatTime(packet.claimTime) }}</text>
          </view>
          <view class="packet-amount">
            <text class="amount-text">+{{ packet.amount }}分</text>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else>
        <view class="empty-icon">🧧</view>
        <text class="empty-text">暂无红包记录</text>
        <text class="empty-desc">完成任务或签到获得红包吧</text>
      </view>
    </view>
    
    <!-- 红包弹窗 -->
    <red-packet-modal 
      :visible="showRedPacket" 
      :packet-data="currentRedPacket"
      @close="handleRedPacketClose"
      @claim="handleRedPacketClaim"
    />
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'
import RedPacketModal from '@/components/RedPacketModal.vue'

export default {
  components: {
    RedPacketModal
  },
  
  data() {
    return {
      showRedPacket: false,
      currentRedPacket: null
    }
  },
  
  computed: {
    redPacketStore() {
      return useRedPacketStore()
    },
    
    isTodaySignedIn() {
      return this.redPacketStore.isTodaySignedIn
    },
    
    consecutiveDays() {
      return this.redPacketStore.getConsecutiveDays
    },
    
    totalAmount() {
      return this.redPacketStore.totalAmount
    },
    
    totalCount() {
      return this.redPacketStore.totalCount
    },
    
    redPacketStats() {
      return this.redPacketStore.getMonthlyStats
    },
    
    taskRedPackets() {
      return this.redPacketStore.taskRedPackets
    },
    
    redPacketHistory() {
      return this.redPacketStore.getRedPacketHistory.slice(0, 20) // 只显示最近20条
    },
    
    signInCalendar() {
      const calendar = []
      const rewards = this.redPacketStore.signInRewards
      
      rewards.forEach((reward, index) => {
        const dayData = {
          day: reward.day,
          reward: reward.amount,
          type: reward.type,
          special: reward.special || false,
          signed: index < this.consecutiveDays,
          isToday: index === this.consecutiveDays && !this.isTodaySignedIn
        }
        calendar.push(dayData)
      })
      
      return calendar
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.updateData()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.redPacketStore.initRedPacketData()
    },
    
    // 更新数据
    updateData() {
      // 数据会通过computed自动更新
    },
    
    // 处理签到
    async handleSignIn() {
      if (this.isTodaySignedIn) {
        uni.showToast({
          title: '今日已签到',
          icon: 'none'
        })
        return
      }
      
      const result = this.redPacketStore.dailySignIn()
      
      if (result.success) {
        // 如果获得红包，显示红包弹窗
        if (result.reward.type === 'redpacket') {
          setTimeout(() => {
            this.showSignInRedPacket(result.reward)
          }, 500)
        } else {
          uni.showToast({
            title: `签到成功！获得${result.reward.amount}积分`,
            icon: 'success'
          })
        }
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    },
    
    // 显示签到红包
    showSignInRedPacket(reward) {
      this.currentRedPacket = {
        type: 'signin',
        amount: reward.amount,
        title: '签到红包',
        message: `恭喜您获得第${this.consecutiveDays}天签到奖励！`,
        animation: 'bounce'
      }
      this.showRedPacket = true
    },
    
    // 领取任务红包
    claimTask(task) {
      if (task.claimed) {
        uni.showToast({
          title: '已经领取过了',
          icon: 'none'
        })
        return
      }
      
      const result = this.redPacketStore.completeTaskRedPacket(task.id)
      
      if (result.success) {
        // 显示红包弹窗
        this.currentRedPacket = {
          type: 'task',
          amount: task.amount,
          title: task.name,
          message: '恭喜您完成任务！',
          animation: 'pulse'
        }
        this.showRedPacket = true
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    },
    
    // 获取红包颜色
    getPacketColor(type) {
      const colors = {
        newuser: '#ff6b6b',
        signin: '#4ecdc4',
        task: '#45b7d1',
        special: '#f9ca24'
      }
      return colors[type] || '#ff6b6b'
    },
    
    // 格式化时间
    formatTime(timeString) {
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else if (diff < 2592000000) {
        return `${Math.floor(diff / 86400000)}天前`
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 清空记录
    clearHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有红包记录吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            this.redPacketStore.userRedPackets = []
            this.redPacketStore.totalAmount = 0
            this.redPacketStore.totalCount = 0
            this.redPacketStore.saveRedPacketData()
            
            uni.showToast({
              title: '已清空记录',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 处理红包弹窗关闭
    handleRedPacketClose() {
      this.showRedPacket = false
      this.currentRedPacket = null
    },
    
    // 处理红包领取
    handleRedPacketClaim(packetData) {
      // 红包已经在store中处理，这里只需要关闭弹窗
      this.handleRedPacketClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.redpacket-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 签到区域 */
.signin-section {
  padding: 32rpx;
}

.signin-card {
  text-align: center;
}

.signin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.signin-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.signin-streak {
  font-size: 26rpx;
  color: var(--danger-color);
  background: rgba(220, 38, 38, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 签到日历 */
.signin-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.calendar-day {
  position: relative;
  padding: 20rpx 8rpx;
  background: var(--bg-color);
  border-radius: 12rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.calendar-day.signed {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--success-color);
}

.calendar-day.today {
  background: rgba(220, 38, 38, 0.1);
  border-color: var(--danger-color);
}

.calendar-day.special {
  background: linear-gradient(135deg, #f9ca24, #f0932b);
  color: white;
}

.day-number {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 4rpx;
}

.calendar-day.special .day-number {
  color: white;
}

.day-reward {
  font-size: 20rpx;
  color: var(--text-light);
  display: block;
}

.calendar-day.special .day-reward {
  color: white;
}

.day-icon {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  font-size: 16rpx;
  color: var(--success-color);
}

/* 任务区域 */
.tasks-section {
  padding: 0 32rpx 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.section-desc {
  font-size: 26rpx;
  color: var(--text-light);
}

.clear-btn {
  font-size: 26rpx;
  color: var(--text-light);
  cursor: pointer;
}

/* 任务列表 */
.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.task-item.completed {
  opacity: 0.6;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 4rpx;
}

.task-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

.task-reward {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.reward-amount {
  font-size: 28rpx;
  color: var(--danger-color);
  font-weight: bold;
}

.claim-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

/* 统计卡片 */
.stats-section {
  padding: 0 32rpx 32rpx;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: var(--shadow);
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--danger-color);
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}

/* 历史记录 */
.history-section {
  padding: 0 32rpx;
}

.history-list {
  background: var(--card-bg);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid var(--border-color);
}

.history-item:last-child {
  border-bottom: none;
}

.packet-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: white;
  margin-right: 24rpx;
}

.packet-info {
  flex: 1;
}

.packet-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 4rpx;
}

.packet-time {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}

.packet-amount {
  text-align: right;
}

.amount-text {
  font-size: 28rpx;
  color: var(--danger-color);
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
  color: var(--text-light);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}
</style>

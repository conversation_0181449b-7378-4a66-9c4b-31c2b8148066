<template>
  <view class="gobang-page">
    <!-- 游戏头部 -->
    <view class="game-header">
      <view class="player-info">
        <text class="player-name">玩家</text>
        <view class="player-piece black"></view>
      </view>
      <view class="game-status">
        <text class="status-text">{{ gameStatusText }}</text>
        <text class="move-count">第{{ moveCount }}步</text>
      </view>
      <view class="player-info">
        <text class="player-name">AI</text>
        <view class="player-piece white"></view>
      </view>
    </view>
    
    <!-- 棋盘容器 -->
    <view class="board-container">
      <canvas 
        class="game-board" 
        canvas-id="gobangBoard"
        @touchstart="onTouchStart"
      ></canvas>
    </view>
    
    <!-- 游戏控制 -->
    <view class="game-controls">
      <button class="control-btn" @click="undoMove" :disabled="!canUndo">
        <text class="btn-icon">↶</text>
        <text class="btn-text">悔棋</text>
      </button>
      <button class="control-btn" @click="restartGame">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重新开始</text>
      </button>
      <button class="control-btn" @click="showHint" :disabled="gameStatus !== 'playing'">
        <text class="btn-icon">💡</text>
        <text class="btn-text">提示</text>
      </button>
    </view>
    
    <!-- 游戏结果弹窗 -->
    <view class="game-result-modal" v-if="showResult">
      <view class="modal-content">
        <view class="result-icon">{{ resultIcon }}</view>
        <text class="result-title">{{ resultTitle }}</text>
        <text class="result-desc">{{ resultDesc }}</text>
        <view class="winning-line" v-if="winningLine.length > 0">
          <text class="winning-text">获胜棋型：{{ getWinningPattern() }}</text>
        </view>
        <view class="result-actions">
          <button class="btn btn-primary" @click="playAgain">再来一局</button>
          <button class="btn btn-outline" @click="closeResult">返回</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 游戏状态
      gameStatus: 'playing', // playing, win, lose, draw
      currentPlayer: 'black', // black(玩家), white(AI)
      moveCount: 0,
      
      // 棋盘数据
      boardSize: 15,
      cellSize: 0,
      board: [],
      moveHistory: [],
      winningLine: [],
      
      // 弹窗状态
      showResult: false,
      
      // Canvas上下文
      ctx: null,
      
      // 提示位置
      hintPosition: null
    }
  },
  
  computed: {
    gameStatusText() {
      switch (this.gameStatus) {
        case 'playing':
          return this.currentPlayer === 'black' ? '轮到您下棋' : 'AI思考中...'
        case 'win':
          return '恭喜获胜！'
        case 'lose':
          return '很遗憾失败了'
        case 'draw':
          return '平局'
        default:
          return '游戏进行中'
      }
    },
    
    canUndo() {
      return this.moveHistory.length >= 2 && this.gameStatus === 'playing'
    },
    
    resultIcon() {
      switch (this.gameStatus) {
        case 'win': return '🎉'
        case 'lose': return '😔'
        case 'draw': return '🤝'
        default: return '🎮'
      }
    },
    
    resultTitle() {
      switch (this.gameStatus) {
        case 'win': return '恭喜获胜！'
        case 'lose': return '再接再厉！'
        case 'draw': return '平局'
        default: return '游戏结束'
      }
    },
    
    resultDesc() {
      switch (this.gameStatus) {
        case 'win': return '五子连珠，棋艺不错！'
        case 'lose': return '多练习就能提高棋艺'
        case 'draw': return '棋盘已满，平局'
        default: return ''
      }
    }
  },
  
  onLoad() {
    this.initGame()
  },
  
  onReady() {
    this.initCanvas()
  },
  
  methods: {
    // 初始化游戏
    initGame() {
      this.gameStatus = 'playing'
      this.currentPlayer = 'black'
      this.moveCount = 0
      this.moveHistory = []
      this.winningLine = []
      this.hintPosition = null
      
      // 初始化棋盘
      this.board = []
      for (let i = 0; i < this.boardSize; i++) {
        this.board[i] = []
        for (let j = 0; j < this.boardSize; j++) {
          this.board[i][j] = 0 // 0:空, 1:黑子, 2:白子
        }
      }
    },
    
    // 初始化Canvas
    initCanvas() {
      const query = uni.createSelectorQuery()
      query.select('.game-board').boundingClientRect((rect) => {
        if (rect) {
          this.cellSize = Math.min(rect.width, rect.height) / (this.boardSize + 1)
          this.ctx = uni.createCanvasContext('gobangBoard', this)
          this.drawBoard()
        }
      }).exec()
    },
    
    // 绘制棋盘
    drawBoard() {
      if (!this.ctx) return
      
      const ctx = this.ctx
      const size = this.cellSize
      const boardPixelSize = size * (this.boardSize + 1)
      
      // 清空画布
      ctx.clearRect(0, 0, boardPixelSize, boardPixelSize)
      
      // 绘制背景
      ctx.setFillStyle('#DEB887')
      ctx.fillRect(0, 0, boardPixelSize, boardPixelSize)
      
      // 绘制网格线
      ctx.setStrokeStyle('#8B4513')
      ctx.setLineWidth(1)
      
      for (let i = 1; i <= this.boardSize; i++) {
        // 横线
        ctx.beginPath()
        ctx.moveTo(size, size * i)
        ctx.lineTo(size * this.boardSize, size * i)
        ctx.stroke()
        
        // 竖线
        ctx.beginPath()
        ctx.moveTo(size * i, size)
        ctx.lineTo(size * i, size * this.boardSize)
        ctx.stroke()
      }
      
      // 绘制天元和星位
      const starPoints = [
        [4, 4], [4, 12], [8, 8], [12, 4], [12, 12]
      ]
      
      ctx.setFillStyle('#8B4513')
      starPoints.forEach(([x, y]) => {
        ctx.beginPath()
        ctx.arc(size * (x + 1), size * (y + 1), 4, 0, 2 * Math.PI)
        ctx.fill()
      })
      
      // 绘制棋子
      this.drawPieces()
      
      // 绘制提示
      if (this.hintPosition) {
        this.drawHint(this.hintPosition.x, this.hintPosition.y)
      }
      
      ctx.draw()
    },
    
    // 绘制棋子
    drawPieces() {
      if (!this.ctx) return
      
      const ctx = this.ctx
      const size = this.cellSize
      const radius = size * 0.4
      
      for (let i = 0; i < this.boardSize; i++) {
        for (let j = 0; j < this.boardSize; j++) {
          if (this.board[i][j] !== 0) {
            const x = size * (j + 1)
            const y = size * (i + 1)
            
            // 绘制棋子阴影
            ctx.setFillStyle('rgba(0, 0, 0, 0.3)')
            ctx.beginPath()
            ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI)
            ctx.fill()
            
            // 绘制棋子
            if (this.board[i][j] === 1) {
              // 黑子
              ctx.setFillStyle('#2c3e50')
            } else {
              // 白子
              ctx.setFillStyle('#ecf0f1')
            }
            
            ctx.beginPath()
            ctx.arc(x, y, radius, 0, 2 * Math.PI)
            ctx.fill()
            
            // 绘制棋子边框
            ctx.setStrokeStyle('#34495e')
            ctx.setLineWidth(1)
            ctx.stroke()
            
            // 如果是获胜棋子，添加特殊标记
            if (this.isWinningPiece(i, j)) {
              ctx.setStrokeStyle('#e74c3c')
              ctx.setLineWidth(3)
              ctx.beginPath()
              ctx.arc(x, y, radius + 3, 0, 2 * Math.PI)
              ctx.stroke()
            }
          }
        }
      }
    },
    
    // 绘制提示
    drawHint(x, y) {
      if (!this.ctx) return
      
      const ctx = this.ctx
      const size = this.cellSize
      const centerX = size * (y + 1)
      const centerY = size * (x + 1)
      
      ctx.setStrokeStyle('#3498db')
      ctx.setLineWidth(2)
      ctx.beginPath()
      ctx.arc(centerX, centerY, size * 0.3, 0, 2 * Math.PI)
      ctx.stroke()
    },
    
    // 触摸开始
    onTouchStart(e) {
      if (this.gameStatus !== 'playing' || this.currentPlayer !== 'black') return
      
      const touch = e.touches[0]
      const x = Math.round((touch.y - this.cellSize) / this.cellSize)
      const y = Math.round((touch.x - this.cellSize) / this.cellSize)
      
      if (x >= 0 && x < this.boardSize && y >= 0 && y < this.boardSize) {
        this.placePiece(x, y)
      }
    },
    
    // 放置棋子
    placePiece(x, y) {
      if (this.board[x][y] !== 0) return
      
      // 放置玩家棋子
      this.board[x][y] = 1
      this.moveHistory.push({ x, y, player: 'black' })
      this.moveCount++
      this.hintPosition = null
      
      // 检查游戏结果
      if (this.checkWin(x, y, 1)) {
        this.endGame('win')
        return
      }
      
      if (this.isBoardFull()) {
        this.endGame('draw')
        return
      }
      
      // 切换到AI
      this.currentPlayer = 'white'
      this.drawBoard()
      
      // AI下棋
      setTimeout(() => {
        this.aiMove()
      }, 500)
    },
    
    // AI移动
    aiMove() {
      const move = this.getBestMove()
      if (move) {
        this.board[move.x][move.y] = 2
        this.moveHistory.push({ x: move.x, y: move.y, player: 'white' })
        this.moveCount++
        
        // 检查AI是否获胜
        if (this.checkWin(move.x, move.y, 2)) {
          this.endGame('lose')
          return
        }
        
        if (this.isBoardFull()) {
          this.endGame('draw')
          return
        }
        
        // 切换回玩家
        this.currentPlayer = 'black'
        this.drawBoard()
      }
    },
    
    // 获取AI最佳移动
    getBestMove() {
      // 简化的AI逻辑
      const moves = []
      
      // 寻找空位
      for (let i = 0; i < this.boardSize; i++) {
        for (let j = 0; j < this.boardSize; j++) {
          if (this.board[i][j] === 0) {
            const score = this.evaluatePosition(i, j, 2)
            moves.push({ x: i, y: j, score })
          }
        }
      }
      
      // 按分数排序
      moves.sort((a, b) => b.score - a.score)
      
      return moves.length > 0 ? moves[0] : null
    },
    
    // 评估位置分数
    evaluatePosition(x, y, player) {
      let score = 0
      
      // 检查四个方向
      const directions = [
        [0, 1], [1, 0], [1, 1], [1, -1]
      ]
      
      directions.forEach(([dx, dy]) => {
        score += this.evaluateDirection(x, y, dx, dy, player)
      })
      
      return score
    },
    
    // 评估方向分数
    evaluateDirection(x, y, dx, dy, player) {
      let score = 0
      let count = 1
      
      // 向前检查
      for (let i = 1; i < 5; i++) {
        const nx = x + dx * i
        const ny = y + dy * i
        if (nx >= 0 && nx < this.boardSize && ny >= 0 && ny < this.boardSize) {
          if (this.board[nx][ny] === player) {
            count++
          } else {
            break
          }
        } else {
          break
        }
      }
      
      // 向后检查
      for (let i = 1; i < 5; i++) {
        const nx = x - dx * i
        const ny = y - dy * i
        if (nx >= 0 && nx < this.boardSize && ny >= 0 && ny < this.boardSize) {
          if (this.board[nx][ny] === player) {
            count++
          } else {
            break
          }
        } else {
          break
        }
      }
      
      // 根据连子数量给分
      if (count >= 5) score += 10000
      else if (count === 4) score += 1000
      else if (count === 3) score += 100
      else if (count === 2) score += 10
      
      return score
    },
    
    // 检查获胜
    checkWin(x, y, player) {
      const directions = [
        [0, 1], [1, 0], [1, 1], [1, -1]
      ]
      
      for (const [dx, dy] of directions) {
        let count = 1
        const line = [{ x, y }]
        
        // 向前检查
        for (let i = 1; i < 5; i++) {
          const nx = x + dx * i
          const ny = y + dy * i
          if (nx >= 0 && nx < this.boardSize && ny >= 0 && ny < this.boardSize && this.board[nx][ny] === player) {
            count++
            line.push({ x: nx, y: ny })
          } else {
            break
          }
        }
        
        // 向后检查
        for (let i = 1; i < 5; i++) {
          const nx = x - dx * i
          const ny = y - dy * i
          if (nx >= 0 && nx < this.boardSize && ny >= 0 && ny < this.boardSize && this.board[nx][ny] === player) {
            count++
            line.unshift({ x: nx, y: ny })
          } else {
            break
          }
        }
        
        if (count >= 5) {
          this.winningLine = line.slice(0, 5)
          return true
        }
      }
      
      return false
    },
    
    // 检查是否是获胜棋子
    isWinningPiece(x, y) {
      return this.winningLine.some(pos => pos.x === x && pos.y === y)
    },
    
    // 检查棋盘是否已满
    isBoardFull() {
      for (let i = 0; i < this.boardSize; i++) {
        for (let j = 0; j < this.boardSize; j++) {
          if (this.board[i][j] === 0) {
            return false
          }
        }
      }
      return true
    },
    
    // 悔棋
    undoMove() {
      if (!this.canUndo) return
      
      // 撤销AI的棋子
      const aiMove = this.moveHistory.pop()
      this.board[aiMove.x][aiMove.y] = 0
      
      // 撤销玩家的棋子
      const playerMove = this.moveHistory.pop()
      this.board[playerMove.x][playerMove.y] = 0
      
      this.moveCount = Math.max(0, this.moveCount - 2)
      this.currentPlayer = 'black'
      this.hintPosition = null
      
      this.drawBoard()
      
      uni.showToast({
        title: '已悔棋',
        icon: 'success'
      })
    },
    
    // 重新开始游戏
    restartGame() {
      uni.showModal({
        title: '确认重新开始',
        content: '当前游戏进度将丢失，确定要重新开始吗？',
        success: (res) => {
          if (res.confirm) {
            this.initGame()
            this.drawBoard()
          }
        }
      })
    },
    
    // 显示提示
    showHint() {
      if (this.gameStatus !== 'playing' || this.currentPlayer !== 'black') return
      
      const move = this.getBestMove()
      if (move) {
        this.hintPosition = { x: move.x, y: move.y }
        this.drawBoard()
        
        uni.showToast({
          title: '建议位置已标出',
          icon: 'success'
        })
      }
    },
    
    // 结束游戏
    endGame(result) {
      this.gameStatus = result
      this.showResult = true
      this.drawBoard()
      
      // 记录游戏结果
      this.recordGameResult(result)
      
      // 如果获胜，可能触发红包
      if (result === 'win' && Math.random() < 0.4) {
        setTimeout(() => {
          this.showWinRedPacket()
        }, 2000)
      }
    },
    
    // 显示获胜红包
    showWinRedPacket() {
      uni.$emit('show_redpacket', {
        type: 'special',
        amount: Math.floor(Math.random() * 8) + 3,
        title: '五子棋获胜红包',
        message: '恭喜您在五子棋对弈中获胜！',
        animation: 'pulse'
      })
    },
    
    // 获取获胜棋型
    getWinningPattern() {
      if (this.winningLine.length === 0) return ''
      
      const first = this.winningLine[0]
      const second = this.winningLine[1]
      const dx = second.x - first.x
      const dy = second.y - first.y
      
      if (dx === 0) return '横五'
      if (dy === 0) return '竖五'
      if (dx === dy) return '斜五'
      return '反斜五'
    },
    
    // 再来一局
    playAgain() {
      this.showResult = false
      this.initGame()
      this.drawBoard()
    },
    
    // 关闭结果弹窗
    closeResult() {
      this.showResult = false
      uni.navigateBack()
    },
    
    // 记录游戏结果
    recordGameResult(result) {
      const stats = uni.getStorageSync('game_stats') || {}
      stats.totalGames = (stats.totalGames || 0) + 1
      
      if (result === 'win') {
        stats.wins = (stats.wins || 0) + 1
      }
      
      stats.winRate = Math.round((stats.wins || 0) / stats.totalGames * 100)
      uni.setStorageSync('game_stats', stats)
    }
  }
}
</script>

<style lang="scss" scoped>
.gobang-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  padding: 32rpx;
  display: flex;
  flex-direction: column;
}

/* 游戏头部 */
.game-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius);
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.player-name {
  font-size: 26rpx;
  color: var(--text-light);
}

.player-piece {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #34495e;
}

.player-piece.black {
  background: #2c3e50;
}

.player-piece.white {
  background: #ecf0f1;
}

.game-status {
  text-align: center;
}

.status-text {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.move-count {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}

/* 棋盘容器 */
.board-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
}

.game-board {
  width: 100%;
  max-width: 600rpx;
  height: 600rpx;
  background: #DEB887;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

/* 游戏控制 */
.game-controls {
  display: flex;
  justify-content: center;
  gap: 24rpx;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: white;
  transform: translateY(-4rpx);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
  color: var(--text-color);
}

/* 游戏结果弹窗 */
.game-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin: 32rpx;
  text-align: center;
  max-width: 500rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.result-desc {
  font-size: 28rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 32rpx;
}

.winning-line {
  margin-bottom: 32rpx;
  padding: 16rpx;
  background: rgba(52, 152, 219, 0.1);
  border-radius: var(--radius);
}

.winning-text {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: bold;
}

.result-actions {
  display: flex;
  gap: 24rpx;
}

.result-actions .btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
}
</style>

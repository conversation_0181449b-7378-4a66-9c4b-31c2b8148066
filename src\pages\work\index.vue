<template>
  <view class="work-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-work">
      <text class="header-title">工作工具</text>
      <text class="header-subtitle">提升效率，简化工作</text>
    </view>
    
    <!-- 工具分类 -->
    <view class="tools-section">
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">证件工具</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card" @click="goToTool('idcard')">
            <view class="tool-icon" style="background: #10b981;">
              <text class="icon">🆔</text>
            </view>
            <text class="tool-title">证件号生成</text>
            <text class="tool-desc">生成测试用身份证号</text>
          </view>
        </view>
      </view>
      
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">图像处理</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card" @click="goToTool('watermark')">
            <view class="tool-icon" style="background: #6366f1;">
              <text class="icon">🖼️</text>
            </view>
            <text class="tool-title">图片去水印</text>
            <text class="tool-desc">智能去除图片水印</text>
          </view>
          
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #8b5cf6;">
              <text class="icon">🎬</text>
            </view>
            <text class="tool-title">视频去水印</text>
            <text class="tool-desc">去除视频水印</text>
          </view>
        </view>
      </view>
      
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">文档处理</text>
        </view>
        <view class="tools-grid">
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #ef4444;">
              <text class="icon">📄</text>
            </view>
            <text class="tool-title">PDF工具</text>
            <text class="tool-desc">PDF转换合并</text>
          </view>
          
          <view class="tool-card coming-soon">
            <view class="tool-icon" style="background: #f59e0b;">
              <text class="icon">📝</text>
            </view>
            <text class="tool-title">文本处理</text>
            <text class="tool-desc">文本格式化工具</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 使用统计 -->
    <view class="stats-section">
      <view class="section-header">
        <text class="section-title">使用统计</text>
      </view>
      
      <view class="stats-cards">
        <view class="stat-card">
          <text class="stat-number">{{ toolStats.totalUse }}</text>
          <text class="stat-label">总使用次数</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ toolStats.todayUse }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ toolStats.favoriteTools }}</text>
          <text class="stat-label">常用工具</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      toolStats: {
        totalUse: 0,
        todayUse: 0,
        favoriteTools: 0
      }
    }
  },
  
  onLoad() {
    this.loadToolStats()
  },
  
  onShow() {
    this.loadToolStats()
  },
  
  methods: {
    // 跳转到工具
    goToTool(tool) {
      // 记录工具使用
      this.recordToolUsage(tool)
      
      const toolRoutes = {
        idcard: '/pages/work/idcard/index',
        watermark: '/pages/work/watermark/image'
      }
      
      const route = toolRoutes[tool]
      if (route) {
        uni.navigateTo({
          url: route
        })
      } else {
        uni.showToast({
          title: '功能即将推出',
          icon: 'none'
        })
      }
    },
    
    // 加载工具统计
    loadToolStats() {
      const stats = uni.getStorageSync('tool_stats') || {
        totalUse: 0,
        todayUse: 0,
        tools: {}
      }
      
      // 检查今日使用次数
      const today = new Date().toDateString()
      const lastUseDate = uni.getStorageSync('last_tool_use_date')
      
      if (lastUseDate !== today) {
        stats.todayUse = 0
        uni.setStorageSync('last_tool_use_date', today)
      }
      
      this.toolStats = {
        totalUse: stats.totalUse,
        todayUse: stats.todayUse,
        favoriteTools: Object.keys(stats.tools).length
      }
    },
    
    // 记录工具使用
    recordToolUsage(tool) {
      const stats = uni.getStorageSync('tool_stats') || {
        totalUse: 0,
        todayUse: 0,
        tools: {}
      }
      
      stats.totalUse += 1
      stats.todayUse += 1
      
      if (!stats.tools[tool]) {
        stats.tools[tool] = 0
      }
      stats.tools[tool] += 1
      
      uni.setStorageSync('tool_stats', stats)
      
      // 触发使用工具任务
      this.$store.dispatch('redpacket/checkUserBehavior', 'use_any_tool')
    }
  }
}
</script>

<style lang="scss" scoped>
.work-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 工具区域 */
.tools-section {
  padding: 32rpx;
}

.category-section {
  margin-bottom: 48rpx;
}

.category-header {
  margin-bottom: 24rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.tool-card {
  background: white;
  border-radius: 12px;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  cursor: pointer;
}

.tool-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.tool-card.coming-soon {
  opacity: 0.6;
  cursor: not-allowed;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}

.tool-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
  display: block;
}

.tool-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
  display: block;
}

/* 统计区域 */
.stats-section {
  padding: 0 32rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #10b981;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

@media (max-width: 500rpx) {
  .tools-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<template>
  <view class="work-page">
    <!-- 页面头部 -->
    <view class="page-header gradient-work">
      <text class="header-title">工作工具</text>
      <text class="header-subtitle">提升效率，专业办公</text>
    </view>
    
    <!-- 工具分类 -->
    <view class="tools-container">
      <!-- 证件工具 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">证件工具</text>
          <text class="category-desc">测试用证件号生成与验证</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('idcard')">
            <view class="tool-icon" style="background: var(--success-color);">
              <text class="icon">🆔</text>
            </view>
            <text class="tool-title">证件号生成器</text>
            <text class="tool-desc">生成测试用身份证号</text>
            <view class="tool-features">
              <text class="feature">✓ 符合国标</text>
              <text class="feature">✓ 批量生成</text>
            </view>
          </view>
          
          <view class="tool-card" @click="goToTool('idcard-verify')">
            <view class="tool-icon" style="background: var(--primary-color);">
              <text class="icon">✅</text>
            </view>
            <text class="tool-title">证件号验证</text>
            <text class="tool-desc">验证身份证号有效性</text>
            <view class="tool-features">
              <text class="feature">✓ 实时验证</text>
              <text class="feature">✓ 详细信息</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 图像处理 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">图像处理</text>
          <text class="category-desc">智能去水印，图片优化</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('image-watermark')">
            <view class="tool-icon" style="background: var(--warning-color);">
              <text class="icon">🖼️</text>
            </view>
            <text class="tool-title">图片去水印</text>
            <text class="tool-desc">AI智能去除图片水印</text>
            <view class="tool-features">
              <text class="feature">✓ AI处理</text>
              <text class="feature">✓ 高清输出</text>
            </view>
          </view>
          
          <view class="tool-card" @click="goToTool('image-compress')">
            <view class="tool-icon" style="background: var(--secondary-color);">
              <text class="icon">📦</text>
            </view>
            <text class="tool-title">图片压缩</text>
            <text class="tool-desc">无损压缩，减小体积</text>
            <view class="tool-features">
              <text class="feature">✓ 无损压缩</text>
              <text class="feature">✓ 批量处理</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 视频处理 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">视频处理</text>
          <text class="category-desc">视频编辑，格式转换</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('video-watermark')">
            <view class="tool-icon" style="background: var(--danger-color);">
              <text class="icon">🎬</text>
            </view>
            <text class="tool-title">视频去水印</text>
            <text class="tool-desc">智能去除视频水印</text>
            <view class="tool-features">
              <text class="feature">✓ 智能识别</text>
              <text class="feature">✓ 高质量</text>
            </view>
          </view>
          
          <view class="tool-card" @click="goToTool('video-convert')">
            <view class="tool-icon" style="background: var(--primary-color);">
              <text class="icon">🔄</text>
            </view>
            <text class="tool-title">格式转换</text>
            <text class="tool-desc">视频格式转换工具</text>
            <view class="tool-features">
              <text class="feature">✓ 多格式</text>
              <text class="feature">✓ 快速转换</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 文档工具 -->
      <view class="tool-category">
        <view class="category-header">
          <text class="category-title">文档工具</text>
          <text class="category-desc">文档处理，格式转换</text>
        </view>
        <view class="tools-grid grid-2">
          <view class="tool-card" @click="goToTool('pdf-tools')">
            <view class="tool-icon" style="background: var(--danger-color);">
              <text class="icon">📄</text>
            </view>
            <text class="tool-title">PDF工具</text>
            <text class="tool-desc">PDF合并分割转换</text>
            <view class="tool-features">
              <text class="feature">✓ 多功能</text>
              <text class="feature">✓ 高效率</text>
            </view>
          </view>
          
          <view class="tool-card" @click="goToTool('text-tools')">
            <view class="tool-icon" style="background: var(--success-color);">
              <text class="icon">📝</text>
            </view>
            <text class="tool-title">文本工具</text>
            <text class="tool-desc">文本处理与格式化</text>
            <view class="tool-features">
              <text class="feature">✓ 多格式</text>
              <text class="feature">✓ 批量处理</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 使用统计 -->
    <view class="usage-stats card">
      <view class="stats-header">
        <text class="stats-title">使用统计</text>
        <text class="stats-period">本月数据</text>
      </view>
      <view class="stats-content">
        <view class="stat-item">
          <text class="stat-number">{{ workStats.totalUsage }}</text>
          <text class="stat-label">工具使用次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ workStats.filesProcessed }}</text>
          <text class="stat-label">处理文件数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ workStats.timeSaved }}</text>
          <text class="stat-label">节省时间(分钟)</text>
        </view>
      </view>
    </view>
    
    <!-- 最近使用 -->
    <view class="recent-tools card" v-if="recentTools.length > 0">
      <view class="recent-header">
        <text class="recent-title">最近使用</text>
        <text class="clear-btn" @click="clearRecent">清空</text>
      </view>
      <view class="recent-list">
        <view 
          class="recent-item" 
          v-for="tool in recentTools" 
          :key="tool.id"
          @click="goToTool(tool.id)"
        >
          <view class="recent-icon" :style="{ background: tool.color }">
            <text class="icon">{{ tool.icon }}</text>
          </view>
          <view class="recent-info">
            <text class="recent-name">{{ tool.name }}</text>
            <text class="recent-time">{{ formatTime(tool.lastUsed) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      workStats: {
        totalUsage: 0,
        filesProcessed: 0,
        timeSaved: 0
      },
      
      recentTools: []
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.updateStats()
    this.loadRecentTools()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.loadWorkStats()
      this.loadRecentTools()
    },
    
    // 加载工作统计
    loadWorkStats() {
      const stats = uni.getStorageSync('work_stats') || {}
      this.workStats = {
        totalUsage: stats.totalUsage || 0,
        filesProcessed: stats.filesProcessed || 0,
        timeSaved: stats.timeSaved || 0
      }
    },
    
    // 加载最近使用工具
    loadRecentTools() {
      this.recentTools = uni.getStorageSync('recent_tools') || []
    },
    
    // 更新统计数据
    updateStats() {
      this.loadWorkStats()
    },
    
    // 跳转到工具
    goToTool(toolId) {
      const toolRoutes = {
        'idcard': '/pages/work/idcard/index',
        'idcard-verify': '/pages/work/idcard/verify',
        'image-watermark': '/pages/work/watermark/image',
        'image-compress': '/pages/work/image/compress',
        'video-watermark': '/pages/work/watermark/video',
        'video-convert': '/pages/work/video/convert',
        'pdf-tools': '/pages/work/document/pdf',
        'text-tools': '/pages/work/document/text'
      }
      
      const route = toolRoutes[toolId]
      if (route) {
        // 记录使用历史
        this.recordToolUsage(toolId)
        
        uni.navigateTo({
          url: route
        })
      } else {
        uni.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    },
    
    // 记录工具使用
    recordToolUsage(toolId) {
      const toolInfo = this.getToolInfo(toolId)
      if (!toolInfo) return
      
      // 更新最近使用
      let recent = uni.getStorageSync('recent_tools') || []
      
      // 移除已存在的记录
      recent = recent.filter(item => item.id !== toolId)
      
      // 添加到开头
      recent.unshift({
        ...toolInfo,
        lastUsed: Date.now()
      })
      
      // 只保留最近10个
      recent = recent.slice(0, 10)
      
      uni.setStorageSync('recent_tools', recent)
      this.recentTools = recent
      
      // 更新使用统计
      this.workStats.totalUsage += 1
      uni.setStorageSync('work_stats', this.workStats)
    },
    
    // 获取工具信息
    getToolInfo(toolId) {
      const toolsMap = {
        'idcard': { name: '证件号生成器', icon: '🆔', color: 'var(--success-color)' },
        'idcard-verify': { name: '证件号验证', icon: '✅', color: 'var(--primary-color)' },
        'image-watermark': { name: '图片去水印', icon: '🖼️', color: 'var(--warning-color)' },
        'image-compress': { name: '图片压缩', icon: '📦', color: 'var(--secondary-color)' },
        'video-watermark': { name: '视频去水印', icon: '🎬', color: 'var(--danger-color)' },
        'video-convert': { name: '格式转换', icon: '🔄', color: 'var(--primary-color)' },
        'pdf-tools': { name: 'PDF工具', icon: '📄', color: 'var(--danger-color)' },
        'text-tools': { name: '文本工具', icon: '📝', color: 'var(--success-color)' }
      }
      
      return toolsMap[toolId] ? { id: toolId, ...toolsMap[toolId] } : null
    },
    
    // 清空最近使用
    clearRecent() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空最近使用记录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('recent_tools')
            this.recentTools = []
            uni.showToast({
              title: '已清空',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return `${Math.floor(diff / 86400000)}天前`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.work-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 工具容器 */
.tools-container {
  padding: 32rpx;
}

/* 工具分类 */
.tool-category {
  margin-bottom: 48rpx;
}

.category-header {
  margin-bottom: 24rpx;
}

.category-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.category-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 工具特性 */
.tool-features {
  display: flex;
  gap: 12rpx;
  margin-top: 12rpx;
}

.feature {
  font-size: 20rpx;
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

/* 使用统计 */
.usage-stats {
  margin: 0 32rpx 32rpx;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.stats-period {
  font-size: 26rpx;
  color: var(--text-light);
}

.stats-content {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--success-color);
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}

/* 最近使用 */
.recent-tools {
  margin: 0 32rpx;
}

.recent-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.recent-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.clear-btn {
  font-size: 26rpx;
  color: var(--text-light);
  cursor: pointer;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-item:hover {
  background: #f1f5f9;
}

.recent-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: white;
  margin-right: 24rpx;
}

.recent-info {
  flex: 1;
}

.recent-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 4rpx;
}

.recent-time {
  font-size: 24rpx;
  color: var(--text-light);
  display: block;
}
</style>

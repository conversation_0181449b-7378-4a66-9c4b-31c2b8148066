{"name": "i18n", "description": "lightweight translation module with dynamic json storage", "version": "0.13.4", "homepage": "http://github.com/mashpie/i18n-node", "repository": {"type": "git", "url": "http://github.com/mashpie/i18n-node.git"}, "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/mashpie"}, "main": "./index", "files": ["i18n.js", "index.js", "SECURITY.md"], "keywords": ["template", "i18n", "l10n"], "directories": {"lib": "."}, "dependencies": {"debug": "^4.3.3", "make-plural": "^7.0.0", "math-interval-parser": "^2.0.1", "messageformat": "^2.3.0", "mustache": "^4.2.0", "sprintf-js": "^1.1.2"}, "devDependencies": {"async": "^3.2.2", "cookie-parser": "^1.4.6", "eslint": "^8.5.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-standard": "^4.1.0", "express": "^4.17.2", "husky": "^7.0.4", "lint-staged": "^12.1.4", "mocha": "^9.1.3", "nyc": "^15.1.0", "prettier": "^2.5.1", "should": "^13.2.3", "sinon": "^12.0.1", "zombie": "^6.1.4"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha --exit", "test-ci": "nyc mocha -- --exit", "coverage": "nyc report --reporter=lcov"}, "lint-staged": {"*.js": "eslint --cache --fix"}, "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged"}}}
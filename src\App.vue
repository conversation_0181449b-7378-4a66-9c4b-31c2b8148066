<template>
  <view id="app">
    <!-- 红包弹窗组件 -->
    <red-packet-modal
      :visible="showRedPacket"
      :packet-data="currentRedPacket"
      @close="handleRedPacketClose"
      @claim="handleRedPacketClaim"
    />
  </view>
</template>

<script>
import { useRedPacketStore } from '@/stores/redpacket'
import RedPacketModal from '@/components/RedPacketModal.vue'

export default {
  components: {
    RedPacketModal
  },
  data() {
    return {
      showRedPacket: false,
      currentRedPacket: null
    }
  },
  
  onLaunch() {
    console.log('App Launch')
    this.initApp()
  },
  
  onShow() {
    console.log('App Show')
    this.checkDailyRedPacket()
  },
  
  onHide() {
    console.log('App Hide')
  },
  
  methods: {
    // 初始化应用
    initApp() {
      // 检查新用户红包
      this.checkNewUserRedPacket()

      // 初始化音频系统
      this.initAudioSystem()

      // 监听全局红包显示事件
      uni.$on('show_redpacket', this.showGlobalRedPacket)
    },
    
    // 检查新用户红包
    checkNewUserRedPacket() {
      const isNewUser = !uni.getStorageSync('user_visited')
      if (isNewUser) {
        setTimeout(() => {
          this.showNewUserRedPacket()
        }, 2000)
        uni.setStorageSync('user_visited', true)
      }
    },
    
    // 检查每日签到红包
    checkDailyRedPacket() {
      const today = new Date().toDateString()
      const lastSignIn = uni.getStorageSync('last_signin_date')
      
      if (lastSignIn !== today) {
        // 显示签到提醒
        setTimeout(() => {
          this.showDailySignInReminder()
        }, 1000)
      }
    },
    
    // 显示新用户红包
    showNewUserRedPacket() {
      this.currentRedPacket = {
        type: 'newuser',
        title: '新用户专享红包',
        amount: Math.floor(Math.random() * 50) + 10, // 10-60元随机
        message: '欢迎使用多功能工具箱！',
        animation: 'bounce'
      }
      this.showRedPacket = true
    },
    
    // 显示每日签到提醒
    showDailySignInReminder() {
      uni.showModal({
        title: '每日签到',
        content: '今天还没有签到哦，去领取签到红包吧！',
        confirmText: '去签到',
        cancelText: '稍后',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/redpacket/index'
            })
          }
        }
      })
    },
    
    // 处理红包关闭
    handleRedPacketClose() {
      this.showRedPacket = false
      this.currentRedPacket = null
    },
    
    // 处理红包领取
    handleRedPacketClaim(packetData) {
      const redPacketStore = useRedPacketStore()
      redPacketStore.claimRedPacket(packetData)
      
      // 播放领取音效
      this.playClaimSound()
      
      // 显示成功提示
      uni.showToast({
        title: `获得${packetData.amount}元红包！`,
        icon: 'success',
        duration: 2000
      })
      
      this.handleRedPacketClose()
    },
    
    // 初始化音频系统
    initAudioSystem() {
      // 预加载音效文件
      const audioFiles = [
        '/static/audio/redpacket_open.mp3',
        '/static/audio/coin_drop.mp3',
        '/static/audio/success.mp3'
      ]
      
      audioFiles.forEach(file => {
        const audio = uni.createInnerAudioContext()
        audio.src = file
        audio.preload = true
      })
    },
    
    // 播放领取音效
    playClaimSound() {
      const audio = uni.createInnerAudioContext()
      audio.src = '/static/audio/redpacket_open.mp3'
      audio.play()
    },

    // 显示全局红包
    showGlobalRedPacket(packetData) {
      this.currentRedPacket = packetData
      this.showRedPacket = true
    }
  }
}
</script>

<style lang="scss">
/* 全局样式 */
@import '@/styles/global.scss';

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局变量 */
:root {
  --primary-color: #6366f1;
  --secondary-color: #ec4899;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #dc2626;
  --text-color: #1f2937;
  --text-light: #6b7280;
  --bg-color: #f8fafc;
  --card-bg: #ffffff;
  --border-color: #e5e7eb;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --radius: 12px;
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: bold;
}

.rounded {
  border-radius: var(--radius);
}

.shadow {
  box-shadow: var(--shadow);
}

.p-4 {
  padding: 32rpx;
}

.m-4 {
  margin: 32rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.gradient-entertainment {
  background: linear-gradient(135deg, #ec4899, #f97316);
}

.gradient-work {
  background: linear-gradient(135deg, #10b981, #059669);
}

.gradient-life {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.gradient-redpacket {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}
</style>

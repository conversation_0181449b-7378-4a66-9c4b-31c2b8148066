<template>
  <view class="calculator-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="header-title">科学计算器</text>
      <text class="header-desc">支持基础运算和科学计算</text>
    </view>
    
    <!-- 计算器主体 -->
    <view class="calculator-container">
      <!-- 显示屏 -->
      <view class="display-section">
        <view class="expression-display">
          <text class="expression-text">{{ expression || '0' }}</text>
        </view>
        <view class="result-display">
          <text class="result-text">{{ displayValue }}</text>
        </view>
      </view>
      
      <!-- 功能切换 -->
      <view class="mode-switch">
        <button 
          class="mode-btn" 
          :class="{ active: mode === 'basic' }"
          @click="switchMode('basic')"
        >
          基础
        </button>
        <button 
          class="mode-btn" 
          :class="{ active: mode === 'scientific' }"
          @click="switchMode('scientific')"
        >
          科学
        </button>
      </view>
      
      <!-- 基础计算器 -->
      <view class="basic-calculator" v-if="mode === 'basic'">
        <view class="button-grid">
          <!-- 第一行 -->
          <button class="calc-btn function-btn" @click="clearAll">C</button>
          <button class="calc-btn function-btn" @click="clearEntry">CE</button>
          <button class="calc-btn function-btn" @click="backspace">⌫</button>
          <button class="calc-btn operator-btn" @click="inputOperator('/')">÷</button>
          
          <!-- 第二行 -->
          <button class="calc-btn number-btn" @click="inputNumber('7')">7</button>
          <button class="calc-btn number-btn" @click="inputNumber('8')">8</button>
          <button class="calc-btn number-btn" @click="inputNumber('9')">9</button>
          <button class="calc-btn operator-btn" @click="inputOperator('*')">×</button>
          
          <!-- 第三行 -->
          <button class="calc-btn number-btn" @click="inputNumber('4')">4</button>
          <button class="calc-btn number-btn" @click="inputNumber('5')">5</button>
          <button class="calc-btn number-btn" @click="inputNumber('6')">6</button>
          <button class="calc-btn operator-btn" @click="inputOperator('-')">-</button>
          
          <!-- 第四行 -->
          <button class="calc-btn number-btn" @click="inputNumber('1')">1</button>
          <button class="calc-btn number-btn" @click="inputNumber('2')">2</button>
          <button class="calc-btn number-btn" @click="inputNumber('3')">3</button>
          <button class="calc-btn operator-btn" @click="inputOperator('+')">+</button>
          
          <!-- 第五行 -->
          <button class="calc-btn number-btn zero-btn" @click="inputNumber('0')">0</button>
          <button class="calc-btn number-btn" @click="inputDecimal()">.</button>
          <button class="calc-btn equals-btn" @click="calculate()">=</button>
        </view>
      </view>
      
      <!-- 科学计算器 -->
      <view class="scientific-calculator" v-if="mode === 'scientific'">
        <view class="scientific-grid">
          <!-- 科学函数行 -->
          <button class="calc-btn function-btn" @click="inputFunction('sin')">sin</button>
          <button class="calc-btn function-btn" @click="inputFunction('cos')">cos</button>
          <button class="calc-btn function-btn" @click="inputFunction('tan')">tan</button>
          <button class="calc-btn function-btn" @click="inputFunction('log')">log</button>
          
          <button class="calc-btn function-btn" @click="inputFunction('ln')">ln</button>
          <button class="calc-btn function-btn" @click="inputOperator('^')">x^y</button>
          <button class="calc-btn function-btn" @click="inputFunction('sqrt')">√</button>
          <button class="calc-btn function-btn" @click="inputConstant('π')">π</button>
          
          <!-- 基础按钮 -->
          <button class="calc-btn function-btn" @click="clearAll">C</button>
          <button class="calc-btn function-btn" @click="clearEntry">CE</button>
          <button class="calc-btn function-btn" @click="backspace">⌫</button>
          <button class="calc-btn operator-btn" @click="inputOperator('/')">÷</button>
          
          <button class="calc-btn number-btn" @click="inputNumber('7')">7</button>
          <button class="calc-btn number-btn" @click="inputNumber('8')">8</button>
          <button class="calc-btn number-btn" @click="inputNumber('9')">9</button>
          <button class="calc-btn operator-btn" @click="inputOperator('*')">×</button>
          
          <button class="calc-btn number-btn" @click="inputNumber('4')">4</button>
          <button class="calc-btn number-btn" @click="inputNumber('5')">5</button>
          <button class="calc-btn number-btn" @click="inputNumber('6')">6</button>
          <button class="calc-btn operator-btn" @click="inputOperator('-')">-</button>
          
          <button class="calc-btn number-btn" @click="inputNumber('1')">1</button>
          <button class="calc-btn number-btn" @click="inputNumber('2')">2</button>
          <button class="calc-btn number-btn" @click="inputNumber('3')">3</button>
          <button class="calc-btn operator-btn" @click="inputOperator('+')">+</button>
          
          <button class="calc-btn number-btn" @click="inputNumber('0')">0</button>
          <button class="calc-btn number-btn" @click="inputDecimal()">.</button>
          <button class="calc-btn function-btn" @click="inputBracket('(')">(</button>
          <button class="calc-btn function-btn" @click="inputBracket(')')">)</button>
          
          <button class="calc-btn equals-btn equals-wide" @click="calculate()">=</button>
        </view>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section card" v-if="history.length > 0">
      <view class="history-header">
        <text class="history-title">计算历史</text>
        <button class="btn btn-small btn-outline" @click="clearHistory">清空</button>
      </view>
      <view class="history-list">
        <view 
          class="history-item" 
          v-for="(item, index) in history.slice(-5)" 
          :key="index"
          @click="useHistoryItem(item)"
        >
          <text class="history-expression">{{ item.expression }}</text>
          <text class="history-result">= {{ item.result }}</text>
        </view>
      </view>
    </view>
    
    <!-- 快捷功能 -->
    <view class="shortcuts-section card">
      <view class="shortcuts-header">
        <text class="shortcuts-title">快捷功能</text>
      </view>
      <view class="shortcuts-grid">
        <button class="shortcut-btn" @click="showConverter">
          <text class="shortcut-icon">🔄</text>
          <text class="shortcut-text">单位转换</text>
        </button>
        <button class="shortcut-btn" @click="showTipCalculator">
          <text class="shortcut-icon">💰</text>
          <text class="shortcut-text">小费计算</text>
        </button>
        <button class="shortcut-btn" @click="showPercentage">
          <text class="shortcut-icon">📊</text>
          <text class="shortcut-text">百分比</text>
        </button>
        <button class="shortcut-btn" @click="showBMI">
          <text class="shortcut-icon">⚖️</text>
          <text class="shortcut-text">BMI计算</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      mode: 'basic', // basic, scientific
      displayValue: '0',
      expression: '',
      previousValue: null,
      operator: null,
      waitingForOperand: false,
      history: []
    }
  },
  
  onLoad() {
    this.loadHistory()
  },
  
  methods: {
    // 切换模式
    switchMode(newMode) {
      this.mode = newMode
    },
    
    // 输入数字
    inputNumber(num) {
      if (this.waitingForOperand) {
        this.displayValue = num
        this.waitingForOperand = false
      } else {
        this.displayValue = this.displayValue === '0' ? num : this.displayValue + num
      }
      
      this.updateExpression()
    },
    
    // 输入小数点
    inputDecimal() {
      if (this.waitingForOperand) {
        this.displayValue = '0.'
        this.waitingForOperand = false
      } else if (this.displayValue.indexOf('.') === -1) {
        this.displayValue += '.'
      }
      
      this.updateExpression()
    },
    
    // 输入运算符
    inputOperator(nextOperator) {
      const inputValue = parseFloat(this.displayValue)
      
      if (this.previousValue === null) {
        this.previousValue = inputValue
      } else if (this.operator) {
        const currentValue = this.previousValue || 0
        const newValue = this.performCalculation(currentValue, inputValue, this.operator)
        
        this.displayValue = String(newValue)
        this.previousValue = newValue
      }
      
      this.waitingForOperand = true
      this.operator = nextOperator
      
      this.updateExpression()
    },
    
    // 输入函数
    inputFunction(func) {
      const value = parseFloat(this.displayValue)
      let result
      
      switch (func) {
        case 'sin':
          result = Math.sin(value * Math.PI / 180)
          break
        case 'cos':
          result = Math.cos(value * Math.PI / 180)
          break
        case 'tan':
          result = Math.tan(value * Math.PI / 180)
          break
        case 'log':
          result = Math.log10(value)
          break
        case 'ln':
          result = Math.log(value)
          break
        case 'sqrt':
          result = Math.sqrt(value)
          break
        default:
          return
      }
      
      this.displayValue = String(result)
      this.expression = `${func}(${value}) = ${result}`
      this.waitingForOperand = true
    },
    
    // 输入常数
    inputConstant(constant) {
      switch (constant) {
        case 'π':
          this.displayValue = String(Math.PI)
          break
      }
      
      this.updateExpression()
      this.waitingForOperand = true
    },
    
    // 输入括号
    inputBracket(bracket) {
      // 简化的括号处理
      this.displayValue += bracket
      this.updateExpression()
    },
    
    // 执行计算
    performCalculation(firstValue, secondValue, operator) {
      switch (operator) {
        case '+':
          return firstValue + secondValue
        case '-':
          return firstValue - secondValue
        case '*':
          return firstValue * secondValue
        case '/':
          return firstValue / secondValue
        case '^':
          return Math.pow(firstValue, secondValue)
        default:
          return secondValue
      }
    },
    
    // 计算结果
    calculate() {
      const inputValue = parseFloat(this.displayValue)
      
      if (this.previousValue !== null && this.operator) {
        const newValue = this.performCalculation(this.previousValue, inputValue, this.operator)
        
        // 保存到历史记录
        this.saveToHistory(this.expression, newValue)
        
        this.displayValue = String(newValue)
        this.expression = ''
        this.previousValue = null
        this.operator = null
        this.waitingForOperand = true
        
        // 记录使用统计
        this.recordUsage()
      }
    },
    
    // 清空所有
    clearAll() {
      this.displayValue = '0'
      this.expression = ''
      this.previousValue = null
      this.operator = null
      this.waitingForOperand = false
    },
    
    // 清空当前输入
    clearEntry() {
      this.displayValue = '0'
      this.waitingForOperand = false
    },
    
    // 退格
    backspace() {
      if (this.displayValue.length > 1) {
        this.displayValue = this.displayValue.slice(0, -1)
      } else {
        this.displayValue = '0'
      }
      
      this.updateExpression()
    },
    
    // 更新表达式显示
    updateExpression() {
      if (this.previousValue !== null && this.operator) {
        this.expression = `${this.previousValue} ${this.getOperatorSymbol(this.operator)} ${this.displayValue}`
      } else {
        this.expression = this.displayValue
      }
    },
    
    // 获取运算符符号
    getOperatorSymbol(operator) {
      const symbols = {
        '+': '+',
        '-': '-',
        '*': '×',
        '/': '÷',
        '^': '^'
      }
      return symbols[operator] || operator
    },
    
    // 保存到历史记录
    saveToHistory(expression, result) {
      const historyItem = {
        expression: expression,
        result: result,
        timestamp: Date.now()
      }
      
      this.history.push(historyItem)
      
      // 只保留最近50条记录
      if (this.history.length > 50) {
        this.history = this.history.slice(-50)
      }
      
      uni.setStorageSync('calculator_history', this.history)
    },
    
    // 加载历史记录
    loadHistory() {
      this.history = uni.getStorageSync('calculator_history') || []
    },
    
    // 清空历史记录
    clearHistory() {
      this.history = []
      uni.removeStorageSync('calculator_history')
      
      uni.showToast({
        title: '历史记录已清空',
        icon: 'success'
      })
    },
    
    // 使用历史记录项
    useHistoryItem(item) {
      this.displayValue = String(item.result)
      this.expression = item.expression
      this.waitingForOperand = true
    },
    
    // 显示单位转换
    showConverter() {
      uni.navigateTo({
        url: '/pages/life/tools/unit-convert'
      })
    },
    
    // 显示小费计算器
    showTipCalculator() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 显示百分比计算
    showPercentage() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 显示BMI计算
    showBMI() {
      uni.navigateTo({
        url: '/pages/life/health/bmi'
      })
    },
    
    // 记录使用统计
    recordUsage() {
      const stats = uni.getStorageSync('life_stats') || {}
      stats.calculatorUsage = (stats.calculatorUsage || 0) + 1
      uni.setStorageSync('life_stats', stats)
    }
  }
}
</script>

<style lang="scss" scoped>
.calculator-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 16rpx;
}

.header-desc {
  font-size: 26rpx;
  color: var(--text-light);
  display: block;
}

/* 计算器容器 */
.calculator-container {
  background: var(--card-bg);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: var(--shadow);
  margin-bottom: 32rpx;
}

/* 显示屏 */
.display-section {
  background: #1a1a1a;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: right;
}

.expression-display {
  margin-bottom: 16rpx;
  min-height: 40rpx;
}

.expression-text {
  font-size: 28rpx;
  color: #888;
  font-family: 'Courier New', monospace;
}

.result-display {
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.result-text {
  font-size: 64rpx;
  color: white;
  font-weight: 300;
  font-family: 'Courier New', monospace;
}

/* 模式切换 */
.mode-switch {
  display: flex;
  background: var(--bg-color);
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
}

.mode-btn {
  flex: 1;
  padding: 16rpx;
  background: transparent;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-light);
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: var(--primary-color);
  color: white;
}

/* 按钮网格 */
.button-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.scientific-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

/* 计算器按钮 */
.calc-btn {
  height: 100rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calc-btn:active {
  transform: scale(0.95);
}

/* 数字按钮 */
.number-btn {
  background: #f8f9fa;
  color: var(--text-color);
}

.number-btn:hover {
  background: #e9ecef;
}

/* 运算符按钮 */
.operator-btn {
  background: var(--primary-color);
  color: white;
}

.operator-btn:hover {
  background: #5856eb;
}

/* 功能按钮 */
.function-btn {
  background: #6c757d;
  color: white;
  font-size: 24rpx;
}

.function-btn:hover {
  background: #5a6268;
}

/* 等号按钮 */
.equals-btn {
  background: var(--success-color);
  color: white;
  grid-column: span 2;
}

.equals-btn:hover {
  background: #059669;
}

.equals-wide {
  grid-column: span 4;
}

/* 零按钮 */
.zero-btn {
  grid-column: span 2;
}

/* 历史记录 */
.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.history-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.history-list {
  max-height: 300rpx;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
  margin-bottom: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: #f1f5f9;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-expression {
  font-size: 28rpx;
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

.history-result {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

/* 快捷功能 */
.shortcuts-header {
  margin-bottom: 24rpx;
}

.shortcuts-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.shortcut-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: var(--bg-color);
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shortcut-btn:hover {
  background: #f1f5f9;
  transform: translateY(-4rpx);
}

.shortcut-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.shortcut-text {
  font-size: 26rpx;
  color: var(--text-color);
}

@media (max-width: 500rpx) {
  .calc-btn {
    height: 80rpx;
    font-size: 28rpx;
  }
  
  .result-text {
    font-size: 48rpx;
  }
  
  .shortcuts-grid {
    grid-template-columns: 1fr;
  }
}
</style>

export declare const M: {
    readonly 'app.compiler.version': "编译器版本：{version}";
    readonly compiling: "正在编译中...";
    readonly 'dev.performance': "请注意运行模式下，因日志输出、sourcemap 以及未压缩源码等原因，性能和包体积，均不及发行模式。";
    readonly 'dev.performance.nvue': "尤其是 app-nvue 的 sourcemap 影响较大";
    readonly 'dev.performance.mp': "若要正式发布，请点击发行菜单或使用 cli 发布命令进行发布";
    readonly 'build.done': "DONE  Build complete.";
    readonly 'dev.watching.start': "开始差量编译...";
    readonly 'dev.watching.end': "DONE  Build complete. Watching for changes...";
    readonly 'dev.watching.end.pages': "DONE  Build complete. PAGES:{pages}";
    readonly 'dev.watching.end.files': "DONE  Build complete. FILES:{files}";
    readonly 'compiler.build.failed': "编译失败";
    readonly 'stat.warn.appid': "当前应用未配置 appid，无法使用 uni 统计，详情参考：https://ask.dcloud.net.cn/article/36303";
    readonly 'stat.warn.version': "当前应用未配置uni统计版本，默认使用1.0版本；建议使用uni统计2.0版本 ，私有部署数据更安全，代码开源可定制。详情：https://uniapp.dcloud.io/uni-stat";
    readonly 'stat.warn.tip': "已开启 uni统计{version} 版本";
    readonly 'i18n.fallbackLocale.default': "当前应用未在 manifest.json 配置 fallbackLocale，默认使用：{locale}";
    readonly 'i18n.fallbackLocale.missing': "当前应用配置的 fallbackLocale 或 locale 为：{locale}，但 locale 目录缺少该语言文件";
    readonly 'easycom.conflict': "easycom组件冲突：";
    readonly 'mp.component.args[0]': "{0}的第一个参数必须为静态字符串";
    readonly 'mp.component.args[1]': "{0}需要两个参数";
    readonly 'mp.360.unsupported': "vue3暂不支持360小程序";
    readonly 'file.notfound': "{file} 文件不存在";
    readonly 'uts.ios.tips': "项目使用了uts插件，iOS平台uts插件代码修改后需要重新生成[自定义基座](https://uniapp.dcloud.net.cn/tutorial/run/run-app.html#customplayground)才能生效";
    readonly 'uts.android.compiler.server': "项目使用了uts插件，正在安装 uts Android 运行扩展...";
    readonly 'uts.ios.windows.tips': "iOS手机在windows上使用标准基座真机运行无法使用uts插件，如需使用uts插件请提交云端打包自定义基座";
    readonly 'uts.ios.standard.tips': "iOS手机在标准基座真机运行暂不支持uts插件，如需调用uts插件请使用自定义基座";
    readonly 'prompt.run.message': "运行方式：打开 {devtools}, 导入 {outputDir} 运行。";
    readonly 'prompt.run.devtools.app': "HBuilderX";
    readonly 'prompt.run.devtools.mp-alipay': "支付宝小程序开发者工具";
    readonly 'prompt.run.devtools.mp-baidu': "百度开发者工具";
    readonly 'prompt.run.devtools.mp--kuaishou': "快手开发者工具";
    readonly 'prompt.run.devtools.mp-lark': "飞书开发者工具";
    readonly 'prompt.run.devtools.mp-qq': "QQ小程序开发者工具";
    readonly 'prompt.run.devtools.mp-toutiao': "抖音开发者工具";
    readonly 'prompt.run.devtools.mp-weixin': "微信开发者工具";
    readonly 'prompt.run.devtools.mp-jd': "京东开发者工具";
    readonly 'prompt.run.devtools.mp-xhs': "小红书开发者工具";
    readonly 'prompt.run.devtools.quickapp-webview': "快应用联盟开发者工具 | 华为快应用开发者工具";
    readonly 'prompt.run.devtools.quickapp-webview-huawei': "华为快应用开发者工具";
    readonly 'prompt.run.devtools.quickapp-webview-union': "快应用联盟开发者工具";
    readonly 'uvue.unsupported': "uvue 暂不支持 {platform} 平台";
} | {
    readonly 'app.compiler.version': "Compiler version: {version}";
    readonly compiling: "Compiling...";
    readonly 'dev.performance': "Please note that in running mode, due to log output, sourcemap, and uncompressed source code, the performance and package size are not as good as release mode.";
    readonly 'dev.performance.nvue': "Especially the sourcemap of app-nvue has a greater impact";
    readonly 'dev.performance.mp': "To officially release, please click the release menu or use the cli release command to release";
    readonly 'build.done': "DONE  Build complete.";
    readonly 'dev.watching.start': "Compiling...";
    readonly 'dev.watching.end': "DONE  Build complete. Watching for changes...";
    readonly 'dev.watching.end.pages': "DONE  Build complete. PAGES:{pages}";
    readonly 'dev.watching.end.files': "DONE  Build complete. FILES:{files}";
    readonly 'compiler.build.failed': "Build failed with errors.";
    readonly 'stat.warn.appid': "The current application is not configured with Appid, and uni statistics cannot be used. For details, see https://ask.dcloud.net.cn/article/36303";
    readonly 'stat.warn.version': "The uni statistics version is not configured. The default version is 1.0.uni statistics version 2.0 is recommended, private deployment data is more secure and code is open source and customizable. details: https://uniapp.dcloud.io/uni-stat";
    readonly 'stat.warn.tip': "uni statistics version: {version}";
    readonly 'i18n.fallbackLocale.default': "fallbackLocale is missing in manifest.json, use: {locale}";
    readonly 'i18n.fallbackLocale.missing': "./local/{locale}.json is missing";
    readonly 'easycom.conflict': "easycom component conflict: ";
    readonly 'mp.component.args[0]': "The first parameter of {0} must be a static string";
    readonly 'mp.component.args[1]': "{0} requires two parameters";
    readonly 'mp.360.unsupported': "360 is unsupported";
    readonly 'file.notfound': "{file} is not found";
    readonly 'uts.ios.tips': "The project uses the uts plugin. After the uts plug-in code is modified, the [Custom playground native runner](https://uniapp.dcloud.net.cn/tutorial/run/run-app.html#customplayground) needs to be regenerated to take effect";
    readonly 'uts.android.compiler.server': "The project uses the uts plugin, installing the uts Android runtime extension...";
    readonly 'uts.ios.windows.tips': "When running on Windows to iOS mobile phone, the modification of the uts plugin code needs to be submitted to the cloud to package the custom playground to take effect.";
    readonly 'uts.ios.standard.tips': "When the standard playground runs to an IOS phone, the uts plugin is temporarily not supported. If you need to call the uts plugin, please use a custom playground";
    readonly 'prompt.run.message': "Run method: open {devtools}, import {outputDir} run.";
    readonly 'prompt.run.devtools.app': "HBuilderX";
    readonly 'prompt.run.devtools.mp-alipay': "Alipay Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-baidu': "Baidu Mini Program Devtools";
    readonly 'prompt.run.devtools.mp--kuaishou': "Kuaishou Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-lark': "Lark Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-qq': "QQ Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-toutiao': "Douyin Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-weixin': "Weixin Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-jd': "Jingdong Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-xhs': "Xiaohongshu Mini Program Devtools";
    readonly 'prompt.run.devtools.quickapp-webview': "Quick App Alliance Devtools | Huawei Quick App Devtools";
    readonly 'prompt.run.devtools.quickapp-webview-huawei': "Huawei Quick App Devtools";
    readonly 'prompt.run.devtools.quickapp-webview-union': "Quick App Alliance Devtools";
    readonly 'uvue.unsupported': "uvue does not support {platform} platform";
};

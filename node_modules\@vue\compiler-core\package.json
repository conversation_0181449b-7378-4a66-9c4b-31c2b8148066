{"name": "@vue/compiler-core", "version": "3.2.47", "description": "@vue/compiler-core", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "types": "dist/compiler-core.d.ts", "files": ["index.js", "dist"], "buildOptions": {"name": "VueCompilerCore", "compat": true, "formats": ["esm-bundler", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-core"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-core#readme", "dependencies": {"@vue/shared": "3.2.47", "@babel/parser": "^7.16.4", "estree-walker": "^2.0.2", "source-map": "^0.6.1"}, "devDependencies": {"@babel/types": "^7.16.0"}}